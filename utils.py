"""
Utility module with shared indicators and helper functions.
Contains technical indicators, market data providers, and common utilities.
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
import logging
from datetime import datetime, timedelta
import requests
import time


class TechnicalIndicators:
    """
    Collection of technical indicator calculations.
    """
    
    @staticmethod
    def rsi(prices: List[float], period: int = 14) -> List[float]:
        """
        Calculate Relative Strength Index (RSI).
        
        Args:
            prices: List of closing prices
            period: RSI period (default: 14)
            
        Returns:
            list: RSI values
        """
        if len(prices) < period + 1:
            return [50.0] * len(prices)  # Default RSI value
        
        # TODO: Implement proper RSI calculation
        # 1. Calculate price changes
        # 2. Separate gains and losses
        # 3. Calculate average gains and losses
        # 4. Calculate RS and RSI
        
        # Placeholder implementation
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gains = []
        avg_losses = []
        rsi_values = []
        
        for i in range(len(gains)):
            if i < period - 1:
                rsi_values.append(50.0)
            else:
                period_gains = gains[max(0, i - period + 1):i + 1]
                period_losses = losses[max(0, i - period + 1):i + 1]
                
                avg_gain = np.mean(period_gains)
                avg_loss = np.mean(period_losses)
                
                if avg_loss == 0:
                    rsi = 100
                else:
                    rs = avg_gain / avg_loss
                    rsi = 100 - (100 / (1 + rs))
                
                rsi_values.append(rsi)
        
        return rsi_values
    
    @staticmethod
    def sma(prices: List[float], period: int) -> List[float]:
        """
        Calculate Simple Moving Average (SMA).
        
        Args:
            prices: List of prices
            period: Moving average period
            
        Returns:
            list: SMA values
        """
        if len(prices) < period:
            return [np.mean(prices)] * len(prices)
        
        sma_values = []
        for i in range(len(prices)):
            if i < period - 1:
                sma_values.append(np.mean(prices[:i + 1]))
            else:
                sma_values.append(np.mean(prices[i - period + 1:i + 1]))
        
        return sma_values
    
    @staticmethod
    def ema(prices: List[float], period: int) -> List[float]:
        """
        Calculate Exponential Moving Average (EMA).
        
        Args:
            prices: List of prices
            period: EMA period
            
        Returns:
            list: EMA values
        """
        if not prices:
            return []
        
        multiplier = 2 / (period + 1)
        ema_values = [prices[0]]  # First EMA value is the first price
        
        for i in range(1, len(prices)):
            ema = (prices[i] * multiplier) + (ema_values[-1] * (1 - multiplier))
            ema_values.append(ema)
        
        return ema_values
    
    @staticmethod
    def macd(prices: List[float], fast_period: int = 12, slow_period: int = 26, 
             signal_period: int = 9) -> Dict[str, List[float]]:
        """
        Calculate MACD (Moving Average Convergence Divergence).
        
        Args:
            prices: List of closing prices
            fast_period: Fast EMA period
            slow_period: Slow EMA period
            signal_period: Signal line EMA period
            
        Returns:
            dict: Dictionary with MACD line, signal line, and histogram
        """
        fast_ema = TechnicalIndicators.ema(prices, fast_period)
        slow_ema = TechnicalIndicators.ema(prices, slow_period)
        
        # MACD line
        macd_line = [fast - slow for fast, slow in zip(fast_ema, slow_ema)]
        
        # Signal line
        signal_line = TechnicalIndicators.ema(macd_line, signal_period)
        
        # Histogram
        histogram = [macd - signal for macd, signal in zip(macd_line, signal_line)]
        
        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }
    
    @staticmethod
    def bollinger_bands(prices: List[float], period: int = 20, 
                       std_dev: float = 2) -> Dict[str, List[float]]:
        """
        Calculate Bollinger Bands.
        
        Args:
            prices: List of closing prices
            period: Moving average period
            std_dev: Standard deviation multiplier
            
        Returns:
            dict: Dictionary with upper, middle, and lower bands
        """
        sma_values = TechnicalIndicators.sma(prices, period)
        
        upper_band = []
        lower_band = []
        
        for i in range(len(prices)):
            if i < period - 1:
                std = np.std(prices[:i + 1])
            else:
                std = np.std(prices[i - period + 1:i + 1])
            
            upper_band.append(sma_values[i] + (std_dev * std))
            lower_band.append(sma_values[i] - (std_dev * std))
        
        return {
            'upper': upper_band,
            'middle': sma_values,
            'lower': lower_band
        }
    
    @staticmethod
    def atr(high: List[float], low: List[float], close: List[float], 
            period: int = 14) -> List[float]:
        """
        Calculate Average True Range (ATR).
        
        Args:
            high: List of high prices
            low: List of low prices
            close: List of closing prices
            period: ATR period
            
        Returns:
            list: ATR values
        """
        if len(high) != len(low) or len(low) != len(close):
            raise ValueError("High, low, and close lists must have the same length")
        
        true_ranges = []
        
        for i in range(len(high)):
            if i == 0:
                tr = high[i] - low[i]
            else:
                tr1 = high[i] - low[i]
                tr2 = abs(high[i] - close[i - 1])
                tr3 = abs(low[i] - close[i - 1])
                tr = max(tr1, tr2, tr3)
            
            true_ranges.append(tr)
        
        # Calculate ATR using SMA of true ranges
        atr_values = TechnicalIndicators.sma(true_ranges, period)
        
        return atr_values
    
    @staticmethod
    def stochastic(high: List[float], low: List[float], close: List[float], 
                   k_period: int = 14, d_period: int = 3) -> Dict[str, List[float]]:
        """
        Calculate Stochastic Oscillator.
        
        Args:
            high: List of high prices
            low: List of low prices
            close: List of closing prices
            k_period: %K period
            d_period: %D period
            
        Returns:
            dict: Dictionary with %K and %D values
        """
        k_values = []
        
        for i in range(len(close)):
            if i < k_period - 1:
                k_values.append(50.0)  # Default value
            else:
                period_high = max(high[i - k_period + 1:i + 1])
                period_low = min(low[i - k_period + 1:i + 1])
                
                if period_high == period_low:
                    k = 50.0
                else:
                    k = ((close[i] - period_low) / (period_high - period_low)) * 100
                
                k_values.append(k)
        
        # %D is SMA of %K
        d_values = TechnicalIndicators.sma(k_values, d_period)
        
        return {
            'k': k_values,
            'd': d_values
        }


class MarketDataProvider:
    """
    Provides market data from various sources.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize market data provider.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.exchange = config.get('exchange', 'binance')
        self.api_key = config.get('api_key')
        self.api_secret = config.get('api_secret')
        self.logger = logging.getLogger(__name__)
        self.cache = {}
        self.cache_duration = config.get('cache_duration', 60)  # seconds
    
    def get_current_data(self, symbols: List[str] = None, 
                        timeframes: List[str] = None) -> Dict[str, Any]:
        """
        Get current market data for specified symbols and timeframes.
        
        Args:
            symbols: List of trading symbols
            timeframes: List of timeframes
            
        Returns:
            dict: Market data dictionary
        """
        if symbols is None:
            symbols = ['BTCUSDT', 'ETHUSDT']
        
        if timeframes is None:
            timeframes = ['1h', '4h']
        
        market_data = {}
        
        for symbol in symbols:
            market_data[symbol] = {}
            
            for timeframe in timeframes:
                try:
                    # Check cache first
                    cache_key = f"{symbol}_{timeframe}"
                    if self._is_cache_valid(cache_key):
                        market_data[symbol][timeframe] = self.cache[cache_key]['data']
                        continue
                    
                    # Fetch new data
                    ohlcv_data = self._fetch_ohlcv(symbol, timeframe)
                    if ohlcv_data:
                        # Calculate indicators
                        indicators = self._calculate_indicators(ohlcv_data)
                        
                        # Combine data
                        tf_data = {**ohlcv_data, **indicators}
                        market_data[symbol][timeframe] = tf_data
                        
                        # Update cache
                        self.cache[cache_key] = {
                            'data': tf_data,
                            'timestamp': datetime.now()
                        }
                    
                except Exception as e:
                    self.logger.error(f"Error fetching data for {symbol} {timeframe}: {e}")
        
        return market_data
    
    def _fetch_ohlcv(self, symbol: str, timeframe: str, limit: int = 100) -> Dict[str, Any]:
        """
        Fetch OHLCV data from exchange.
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe
            limit: Number of candles to fetch
            
        Returns:
            dict: OHLCV data
        """
        # TODO: Implement actual exchange API calls
        # This is a placeholder implementation
        
        # Generate dummy data for testing
        import random
        
        base_price = 50000 if 'BTC' in symbol else 3000
        
        timestamps = []
        opens = []
        highs = []
        lows = []
        closes = []
        volumes = []
        
        current_price = base_price
        
        for i in range(limit):
            # Generate realistic price movement
            change_pct = random.uniform(-0.02, 0.02)  # ±2% change
            new_price = current_price * (1 + change_pct)
            
            open_price = current_price
            close_price = new_price
            high_price = max(open_price, close_price) * random.uniform(1.0, 1.01)
            low_price = min(open_price, close_price) * random.uniform(0.99, 1.0)
            volume = random.uniform(100, 1000)
            
            timestamps.append(datetime.now() - timedelta(hours=limit - i))
            opens.append(open_price)
            highs.append(high_price)
            lows.append(low_price)
            closes.append(close_price)
            volumes.append(volume)
            
            current_price = new_price
        
        return {
            'symbol': symbol,
            'timeframe': timeframe,
            'timestamp': timestamps,
            'open': opens,
            'high': highs,
            'low': lows,
            'close': closes,
            'volume': volumes
        }
    
    def _calculate_indicators(self, ohlcv_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate technical indicators for OHLCV data.
        
        Args:
            ohlcv_data: OHLCV data dictionary
            
        Returns:
            dict: Dictionary with calculated indicators
        """
        closes = ohlcv_data['close']
        highs = ohlcv_data['high']
        lows = ohlcv_data['low']
        
        indicators = {}
        
        try:
            # Calculate various indicators
            indicators['rsi'] = TechnicalIndicators.rsi(closes, 14)
            indicators['sma_20'] = TechnicalIndicators.sma(closes, 20)
            indicators['sma_50'] = TechnicalIndicators.sma(closes, 50)
            indicators['ema_12'] = TechnicalIndicators.ema(closes, 12)
            indicators['ema_26'] = TechnicalIndicators.ema(closes, 26)
            
            macd_data = TechnicalIndicators.macd(closes)
            indicators.update(macd_data)
            
            bb_data = TechnicalIndicators.bollinger_bands(closes)
            indicators['bb_upper'] = bb_data['upper']
            indicators['bb_middle'] = bb_data['middle']
            indicators['bb_lower'] = bb_data['lower']
            
            indicators['atr'] = TechnicalIndicators.atr(highs, lows, closes)
            
            stoch_data = TechnicalIndicators.stochastic(highs, lows, closes)
            indicators['stoch_k'] = stoch_data['k']
            indicators['stoch_d'] = stoch_data['d']
            
        except Exception as e:
            self.logger.error(f"Error calculating indicators: {e}")
        
        return indicators
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """
        Check if cached data is still valid.
        
        Args:
            cache_key: Cache key
            
        Returns:
            bool: True if cache is valid
        """
        if cache_key not in self.cache:
            return False
        
        cache_time = self.cache[cache_key]['timestamp']
        return (datetime.now() - cache_time).total_seconds() < self.cache_duration


class UtilityFunctions:
    """
    Collection of utility functions.
    """
    
    @staticmethod
    def calculate_position_value(price: float, quantity: float) -> float:
        """
        Calculate position value.
        
        Args:
            price: Asset price
            quantity: Quantity
            
        Returns:
            float: Position value
        """
        return price * quantity
    
    @staticmethod
    def calculate_percentage_change(old_value: float, new_value: float) -> float:
        """
        Calculate percentage change between two values.
        
        Args:
            old_value: Original value
            new_value: New value
            
        Returns:
            float: Percentage change
        """
        if old_value == 0:
            return 0.0
        
        return ((new_value - old_value) / old_value) * 100
    
    @staticmethod
    def round_to_precision(value: float, precision: int) -> float:
        """
        Round value to specified precision.
        
        Args:
            value: Value to round
            precision: Number of decimal places
            
        Returns:
            float: Rounded value
        """
        return round(value, precision)
    
    @staticmethod
    def validate_symbol(symbol: str) -> bool:
        """
        Validate trading symbol format.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            bool: True if symbol is valid
        """
        # Basic validation - should be improved based on exchange requirements
        return len(symbol) >= 6 and symbol.isalnum() and symbol.isupper()
    
    @staticmethod
    def format_currency(amount: float, currency: str = 'USD') -> str:
        """
        Format currency amount for display.
        
        Args:
            amount: Amount to format
            currency: Currency code
            
        Returns:
            str: Formatted currency string
        """
        if currency == 'USD':
            return f"${amount:,.2f}"
        else:
            return f"{amount:,.6f} {currency}"
    
    @staticmethod
    def calculate_risk_reward_ratio(entry_price: float, stop_loss: float, 
                                  take_profit: float) -> float:
        """
        Calculate risk/reward ratio.
        
        Args:
            entry_price: Entry price
            stop_loss: Stop loss price
            take_profit: Take profit price
            
        Returns:
            float: Risk/reward ratio
        """
        risk = abs(entry_price - stop_loss)
        reward = abs(take_profit - entry_price)
        
        if risk == 0:
            return 0.0
        
        return reward / risk
