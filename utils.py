"""
Utility Module - Technical Indicators and Helper Functions

Contains shared technical indicators and utility functions used by all strategies.
Uses pandas and ta library for reliable technical analysis calculations.

Functions:
- calculate_rsi(data, period): Calculate Relative Strength Index
- calculate_atr(data, period): Calculate Average True Range  
- detect_rsi_divergence(data): Detect RSI divergence patterns
- calculate_sma(data, period): Calculate Simple Moving Average
- calculate_ema(data, period): Calculate Exponential Moving Average
- calculate_macd(data): Calculate MACD indicator
- calculate_bollinger_bands(data, period, std): Calculate Bollinger Bands

Requirements:
    pip install pandas ta numpy scipy
"""

import pandas as pd
import numpy as np
import ta
from typing import Dict, Any, Optional, List
import logging
from datetime import datetime, timedelta
from scipy.signal import argrelextrema


def calculate_rsi(data: pd.DataFrame, period: int = 14) -> pd.Series:
    """
    Calculate Relative Strength Index (RSI) using ta library.
    
    The RSI is a momentum oscillator that measures the speed and magnitude of price changes.
    RSI oscillates between 0 and 100. Values above 70 are typically considered overbought,
    while values below 30 are considered oversold.
    
    Args:
        data: DataFrame with OHLCV data (must contain 'close' column)
        period: RSI calculation period (default: 14)
        
    Returns:
        pd.Series: RSI values
        
    Example:
        >>> df = pd.DataFrame({'close': [100, 102, 101, 103, 105, 104, 106]})
        >>> rsi = calculate_rsi(df, period=6)
        >>> print(rsi.iloc[-1])  # Latest RSI value
        
    Raises:
        ValueError: If data doesn't contain 'close' column
        ValueError: If period is less than 1
    """
    if 'close' not in data.columns:
        raise ValueError("DataFrame must contain 'close' column")
    
    if period < 1:
        raise ValueError("Period must be at least 1")
    
    if len(data) < period:
        logging.warning(f"Data length ({len(data)}) is less than RSI period ({period})")
    
    try:
        # Use ta library for RSI calculation
        rsi = ta.momentum.RSIIndicator(close=data['close'], window=period).rsi()
        
        # Fill NaN values with 50 (neutral RSI)
        rsi = rsi.fillna(50.0)
        
        logging.debug(f"Calculated RSI with period {period}, latest value: {rsi.iloc[-1]:.2f}")
        return rsi
        
    except Exception as e:
        logging.error(f"Error calculating RSI: {e}")
        # Return neutral RSI values as fallback
        return pd.Series([50.0] * len(data), index=data.index)


def calculate_atr(data: pd.DataFrame, period: int = 14) -> pd.Series:
    """
    Calculate Average True Range (ATR) using ta library.
    
    ATR measures market volatility by decomposing the entire range of an asset price
    for that period. Higher ATR values indicate higher volatility.
    
    Args:
        data: DataFrame with OHLCV data (must contain 'high', 'low', 'close' columns)
        period: ATR calculation period (default: 14)
        
    Returns:
        pd.Series: ATR values
        
    Example:
        >>> df = pd.DataFrame({
        ...     'high': [102, 104, 103, 105, 107],
        ...     'low': [98, 100, 99, 101, 103],
        ...     'close': [100, 102, 101, 103, 105]
        ... })
        >>> atr = calculate_atr(df, period=4)
        >>> print(f"Current ATR: {atr.iloc[-1]:.4f}")
        
    Raises:
        ValueError: If data doesn't contain required columns
        ValueError: If period is less than 1
    """
    required_columns = ['high', 'low', 'close']
    missing_columns = [col for col in required_columns if col not in data.columns]
    
    if missing_columns:
        raise ValueError(f"DataFrame must contain columns: {missing_columns}")
    
    if period < 1:
        raise ValueError("Period must be at least 1")
    
    if len(data) < period:
        logging.warning(f"Data length ({len(data)}) is less than ATR period ({period})")
    
    try:
        # Use ta library for ATR calculation
        atr = ta.volatility.AverageTrueRange(
            high=data['high'], 
            low=data['low'], 
            close=data['close'], 
            window=period
        ).average_true_range()
        
        # Fill NaN values with the first valid ATR value
        first_valid_atr = atr.dropna().iloc[0] if not atr.dropna().empty else 0.01
        atr = atr.fillna(first_valid_atr)
        
        logging.debug(f"Calculated ATR with period {period}, latest value: {atr.iloc[-1]:.4f}")
        return atr
        
    except Exception as e:
        logging.error(f"Error calculating ATR: {e}")
        # Return small positive values as fallback
        return pd.Series([0.01] * len(data), index=data.index)


def detect_rsi_divergence(data: pd.DataFrame, rsi_period: int = 14, 
                         lookback_period: int = 20, min_peaks: int = 2) -> Dict[str, Any]:
    """
    Detect RSI divergence patterns in price data.
    
    Divergence occurs when price and RSI move in opposite directions:
    - Bullish divergence: Price makes lower lows while RSI makes higher lows
    - Bearish divergence: Price makes higher highs while RSI makes lower highs
    
    Args:
        data: DataFrame with OHLCV data (must contain 'high', 'low', 'close' columns)
        rsi_period: Period for RSI calculation (default: 14)
        lookback_period: Number of periods to look back for divergence (default: 20)
        min_peaks: Minimum number of peaks/troughs required (default: 2)
        
    Returns:
        dict: Divergence analysis results containing:
            - 'bullish_divergence': bool
            - 'bearish_divergence': bool
            - 'divergence_strength': float (0-1)
            - 'price_peaks': list of peak indices
            - 'price_troughs': list of trough indices
            - 'rsi_peaks': list of RSI peak indices
            - 'rsi_troughs': list of RSI trough indices
            - 'latest_signal': str ('bullish', 'bearish', 'none')
            
    Example:
        >>> df = pd.DataFrame({
        ...     'high': [100, 102, 98, 104, 96, 106, 94],
        ...     'low': [98, 100, 96, 102, 94, 104, 92],
        ...     'close': [99, 101, 97, 103, 95, 105, 93]
        ... })
        >>> result = detect_rsi_divergence(df)
        >>> if result['bullish_divergence']:
        ...     print(f"Bullish divergence detected! Strength: {result['divergence_strength']:.2f}")
    """
    try:
        # Validate input data
        required_columns = ['high', 'low', 'close']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            raise ValueError(f"DataFrame must contain columns: {missing_columns}")
        
        if len(data) < max(rsi_period, lookback_period):
            return {
                'bullish_divergence': False,
                'bearish_divergence': False,
                'divergence_strength': 0.0,
                'price_peaks': [],
                'price_troughs': [],
                'rsi_peaks': [],
                'rsi_troughs': [],
                'latest_signal': 'none',
                'error': 'Insufficient data'
            }
        
        # Calculate RSI
        rsi = calculate_rsi(data, rsi_period)
        
        # Get recent data for analysis
        recent_data = data.tail(lookback_period).copy()
        recent_rsi = rsi.tail(lookback_period)
        
        # Find peaks and troughs in price (using highs for peaks, lows for troughs)
        price_highs = recent_data['high'].values
        price_lows = recent_data['low'].values
        rsi_values = recent_rsi.values
        
        # Find local maxima and minima
        price_peak_indices = argrelextrema(price_highs, np.greater, order=2)[0]
        price_trough_indices = argrelextrema(price_lows, np.less, order=2)[0]
        rsi_peak_indices = argrelextrema(rsi_values, np.greater, order=2)[0]
        rsi_trough_indices = argrelextrema(rsi_values, np.less, order=2)[0]
        
        # Ensure we have enough peaks/troughs
        if len(price_peak_indices) < min_peaks or len(price_trough_indices) < min_peaks:
            return {
                'bullish_divergence': False,
                'bearish_divergence': False,
                'divergence_strength': 0.0,
                'price_peaks': price_peak_indices.tolist(),
                'price_troughs': price_trough_indices.tolist(),
                'rsi_peaks': rsi_peak_indices.tolist(),
                'rsi_troughs': rsi_trough_indices.tolist(),
                'latest_signal': 'none',
                'error': 'Insufficient peaks/troughs'
            }
        
        # Check for bullish divergence (price lower lows, RSI higher lows)
        bullish_divergence = False
        bearish_divergence = False
        divergence_strength = 0.0
        
        # Bullish divergence analysis
        if len(price_trough_indices) >= 2 and len(rsi_trough_indices) >= 2:
            # Get last two price troughs
            last_price_troughs = price_trough_indices[-2:]
            # Find corresponding RSI troughs (closest in time)
            corresponding_rsi_troughs = []
            
            for price_idx in last_price_troughs:
                # Find closest RSI trough
                closest_rsi_idx = min(rsi_trough_indices, 
                                    key=lambda x: abs(x - price_idx))
                corresponding_rsi_troughs.append(closest_rsi_idx)
            
            if len(corresponding_rsi_troughs) == 2:
                price_trough_1 = price_lows[last_price_troughs[0]]
                price_trough_2 = price_lows[last_price_troughs[1]]
                rsi_trough_1 = rsi_values[corresponding_rsi_troughs[0]]
                rsi_trough_2 = rsi_values[corresponding_rsi_troughs[1]]
                
                # Bullish divergence: price makes lower low, RSI makes higher low
                if price_trough_2 < price_trough_1 and rsi_trough_2 > rsi_trough_1:
                    bullish_divergence = True
                    # Calculate strength based on the magnitude of divergence
                    price_change = abs(price_trough_2 - price_trough_1) / price_trough_1
                    rsi_change = abs(rsi_trough_2 - rsi_trough_1) / 100  # RSI is 0-100
                    divergence_strength = min(1.0, (price_change + rsi_change) / 2)
        
        # Bearish divergence analysis
        if len(price_peak_indices) >= 2 and len(rsi_peak_indices) >= 2:
            # Get last two price peaks
            last_price_peaks = price_peak_indices[-2:]
            # Find corresponding RSI peaks
            corresponding_rsi_peaks = []
            
            for price_idx in last_price_peaks:
                # Find closest RSI peak
                closest_rsi_idx = min(rsi_peak_indices, 
                                    key=lambda x: abs(x - price_idx))
                corresponding_rsi_peaks.append(closest_rsi_idx)
            
            if len(corresponding_rsi_peaks) == 2:
                price_peak_1 = price_highs[last_price_peaks[0]]
                price_peak_2 = price_highs[last_price_peaks[1]]
                rsi_peak_1 = rsi_values[corresponding_rsi_peaks[0]]
                rsi_peak_2 = rsi_values[corresponding_rsi_peaks[1]]
                
                # Bearish divergence: price makes higher high, RSI makes lower high
                if price_peak_2 > price_peak_1 and rsi_peak_2 < rsi_peak_1:
                    bearish_divergence = True
                    # Calculate strength
                    price_change = abs(price_peak_2 - price_peak_1) / price_peak_1
                    rsi_change = abs(rsi_peak_2 - rsi_peak_1) / 100
                    divergence_strength = max(divergence_strength, 
                                            min(1.0, (price_change + rsi_change) / 2))
        
        # Determine latest signal
        latest_signal = 'none'
        if bullish_divergence and bearish_divergence:
            # If both detected, choose the stronger one
            latest_signal = 'bullish' if bullish_divergence else 'bearish'
        elif bullish_divergence:
            latest_signal = 'bullish'
        elif bearish_divergence:
            latest_signal = 'bearish'
        
        result = {
            'bullish_divergence': bullish_divergence,
            'bearish_divergence': bearish_divergence,
            'divergence_strength': round(divergence_strength, 3),
            'price_peaks': price_peak_indices.tolist(),
            'price_troughs': price_trough_indices.tolist(),
            'rsi_peaks': rsi_peak_indices.tolist(),
            'rsi_troughs': rsi_trough_indices.tolist(),
            'latest_signal': latest_signal
        }
        
        logging.debug(f"RSI divergence analysis: {latest_signal} signal, strength: {divergence_strength:.3f}")
        return result
        
    except Exception as e:
        logging.error(f"Error detecting RSI divergence: {e}")
        return {
            'bullish_divergence': False,
            'bearish_divergence': False,
            'divergence_strength': 0.0,
            'price_peaks': [],
            'price_troughs': [],
            'rsi_peaks': [],
            'rsi_troughs': [],
            'latest_signal': 'none',
            'error': str(e)
        }


def calculate_sma(data: pd.DataFrame, period: int = 20, column: str = 'close') -> pd.Series:
    """
    Calculate Simple Moving Average (SMA).

    Args:
        data: DataFrame with price data
        period: SMA period (default: 20)
        column: Column to calculate SMA for (default: 'close')

    Returns:
        pd.Series: SMA values
    """
    if column not in data.columns:
        raise ValueError(f"DataFrame must contain '{column}' column")

    try:
        sma = ta.trend.SMAIndicator(close=data[column], window=period).sma_indicator()
        return sma.bfill()
    except Exception as e:
        logging.error(f"Error calculating SMA: {e}")
        return pd.Series([data[column].mean()] * len(data), index=data.index)


def calculate_ema(data: pd.DataFrame, period: int = 20, column: str = 'close') -> pd.Series:
    """
    Calculate Exponential Moving Average (EMA).

    Args:
        data: DataFrame with price data
        period: EMA period (default: 20)
        column: Column to calculate EMA for (default: 'close')

    Returns:
        pd.Series: EMA values
    """
    if column not in data.columns:
        raise ValueError(f"DataFrame must contain '{column}' column")

    try:
        ema = ta.trend.EMAIndicator(close=data[column], window=period).ema_indicator()
        return ema.bfill()
    except Exception as e:
        logging.error(f"Error calculating EMA: {e}")
        return pd.Series([data[column].mean()] * len(data), index=data.index)


def calculate_macd(data: pd.DataFrame, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
    """
    Calculate MACD (Moving Average Convergence Divergence).

    Args:
        data: DataFrame with price data (must contain 'close' column)
        fast: Fast EMA period (default: 12)
        slow: Slow EMA period (default: 26)
        signal: Signal line EMA period (default: 9)

    Returns:
        dict: Dictionary containing 'macd', 'signal', and 'histogram' Series
    """
    if 'close' not in data.columns:
        raise ValueError("DataFrame must contain 'close' column")

    try:
        macd_indicator = ta.trend.MACD(close=data['close'], window_fast=fast, window_slow=slow, window_sign=signal)

        return {
            'macd': macd_indicator.macd().fillna(0),
            'signal': macd_indicator.macd_signal().fillna(0),
            'histogram': macd_indicator.macd_diff().fillna(0)
        }
    except Exception as e:
        logging.error(f"Error calculating MACD: {e}")
        return {
            'macd': pd.Series([0] * len(data), index=data.index),
            'signal': pd.Series([0] * len(data), index=data.index),
            'histogram': pd.Series([0] * len(data), index=data.index)
        }


def calculate_bollinger_bands(data: pd.DataFrame, period: int = 20, std: float = 2.0) -> Dict[str, pd.Series]:
    """
    Calculate Bollinger Bands.

    Args:
        data: DataFrame with price data (must contain 'close' column)
        period: Moving average period (default: 20)
        std: Standard deviation multiplier (default: 2.0)

    Returns:
        dict: Dictionary containing 'upper', 'middle', and 'lower' bands
    """
    if 'close' not in data.columns:
        raise ValueError("DataFrame must contain 'close' column")

    try:
        bb_indicator = ta.volatility.BollingerBands(close=data['close'], window=period, window_dev=std)

        return {
            'upper': bb_indicator.bollinger_hband().bfill(),
            'middle': bb_indicator.bollinger_mavg().bfill(),
            'lower': bb_indicator.bollinger_lband().bfill()
        }
    except Exception as e:
        logging.error(f"Error calculating Bollinger Bands: {e}")
        close_mean = data['close'].mean()
        return {
            'upper': pd.Series([close_mean * 1.02] * len(data), index=data.index),
            'middle': pd.Series([close_mean] * len(data), index=data.index),
            'lower': pd.Series([close_mean * 0.98] * len(data), index=data.index)
        }


class MarketDataProvider:
    """
    Provides market data for backtesting and live trading.
    Currently simulates data - can be extended for real exchange integration.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize market data provider.

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.cache = {}
        self.cache_duration = config.get('market_data', {}).get('cache_duration', 60)

    def get_current_data(self, symbols: List[str] = None, timeframes: List[str] = None) -> Dict[str, Any]:
        """
        Get current market data for specified symbols and timeframes.

        Args:
            symbols: List of symbols to get data for
            timeframes: List of timeframes to get data for

        Returns:
            dict: Market data organized by symbol and timeframe
        """
        if symbols is None:
            symbols = ['BTCUSDT', 'ETHUSDT']

        if timeframes is None:
            timeframes = ['1h', '4h']

        market_data = {}

        for symbol in symbols:
            market_data[symbol] = {}

            for timeframe in timeframes:
                try:
                    # Generate simulated OHLCV data
                    df = self._generate_sample_data(symbol, timeframe)

                    # Calculate indicators
                    df_with_indicators = self._add_indicators(df)

                    market_data[symbol][timeframe] = df_with_indicators

                except Exception as e:
                    self.logger.error(f"Error getting data for {symbol} {timeframe}: {e}")
                    market_data[symbol][timeframe] = {}

        return market_data

    def _generate_sample_data(self, symbol: str, timeframe: str, periods: int = 100) -> pd.DataFrame:
        """
        Generate sample OHLCV data for testing.

        Args:
            symbol: Trading symbol
            timeframe: Data timeframe
            periods: Number of periods to generate

        Returns:
            pd.DataFrame: Sample OHLCV data
        """
        # Base prices for different symbols
        base_prices = {
            'BTCUSDT': 45000,
            'ETHUSDT': 2800,
            'ADAUSDT': 0.45,
            'DOTUSDT': 7.5,
            'LINKUSDT': 15.0
        }

        base_price = base_prices.get(symbol, 100)

        # Generate random price movements
        np.random.seed(hash(symbol + timeframe) % 2**32)  # Consistent data for same symbol/timeframe

        # Generate date range with hourly frequency
        dates = pd.date_range(end=datetime.now(), periods=periods, freq='h')

        # Generate price series with some trend and volatility
        returns = np.random.normal(0.0001, 0.02, periods)  # Small positive drift with 2% volatility
        prices = [base_price]

        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(new_price)

        # Generate OHLC from close prices
        data = []
        for i, close in enumerate(prices):
            if i == 0:
                open_price = close
            else:
                open_price = prices[i-1]

            # Generate high and low around open/close
            high = max(open_price, close) * np.random.uniform(1.0, 1.015)
            low = min(open_price, close) * np.random.uniform(0.985, 1.0)
            volume = np.random.uniform(100, 1000)

            data.append({
                'timestamp': dates[i],
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume
            })

        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)

        return df

    def _add_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add technical indicators to OHLCV data.

        Args:
            df: OHLCV DataFrame

        Returns:
            pd.DataFrame: DataFrame with indicators added
        """
        try:
            # Add basic indicators
            df['rsi'] = calculate_rsi(df, 14)
            df['atr'] = calculate_atr(df, 14)
            df['sma_20'] = calculate_sma(df, 20)
            df['ema_20'] = calculate_ema(df, 20)

            # Add MACD
            macd_data = calculate_macd(df)
            df['macd'] = macd_data['macd']
            df['macd_signal'] = macd_data['signal']
            df['macd_histogram'] = macd_data['histogram']

            # Add Bollinger Bands
            bb_data = calculate_bollinger_bands(df)
            df['bb_upper'] = bb_data['upper']
            df['bb_middle'] = bb_data['middle']
            df['bb_lower'] = bb_data['lower']

        except Exception as e:
            self.logger.error(f"Error adding indicators: {e}")

        return df


# Example usage and testing
if __name__ == "__main__":
    # Test the utility functions
    print("Testing technical indicators...")

    # Create sample data
    dates = pd.date_range(start='2024-01-01', periods=50, freq='1h')
    np.random.seed(42)

    # Generate sample price data
    base_price = 50000
    returns = np.random.normal(0, 0.02, 50)
    prices = [base_price]

    for ret in returns[1:]:
        new_price = prices[-1] * (1 + ret)
        prices.append(new_price)

    # Create DataFrame
    df = pd.DataFrame({
        'timestamp': dates,
        'open': [p * 0.999 for p in prices],
        'high': [p * 1.01 for p in prices],
        'low': [p * 0.99 for p in prices],
        'close': prices,
        'volume': np.random.uniform(100, 1000, 50)
    })
    df.set_index('timestamp', inplace=True)

    print(f"Sample data shape: {df.shape}")

    # Test RSI
    print("\n1. Testing RSI calculation...")
    rsi = calculate_rsi(df)
    print(f"Latest RSI: {rsi.iloc[-1]:.2f}")

    # Test ATR
    print("\n2. Testing ATR calculation...")
    atr = calculate_atr(df)
    print(f"Latest ATR: {atr.iloc[-1]:.4f}")

    # Test RSI divergence detection
    print("\n3. Testing RSI divergence detection...")
    divergence = detect_rsi_divergence(df)
    print(f"Bullish divergence: {divergence['bullish_divergence']}")
    print(f"Bearish divergence: {divergence['bearish_divergence']}")
    print(f"Latest signal: {divergence['latest_signal']}")
    print(f"Divergence strength: {divergence['divergence_strength']:.3f}")

    # Test other indicators
    print("\n4. Testing other indicators...")
    sma = calculate_sma(df, 20)
    ema = calculate_ema(df, 20)
    macd_data = calculate_macd(df)
    bb_data = calculate_bollinger_bands(df)

    print(f"Latest SMA(20): {sma.iloc[-1]:.2f}")
    print(f"Latest EMA(20): {ema.iloc[-1]:.2f}")
    print(f"Latest MACD: {macd_data['macd'].iloc[-1]:.4f}")
    print(f"Latest BB Upper: {bb_data['upper'].iloc[-1]:.2f}")

    print("\n✅ All technical indicator tests completed!")
    print("The utils.py module is ready for use by trading strategies.")
