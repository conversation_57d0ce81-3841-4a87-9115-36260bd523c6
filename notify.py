"""
Notification Module

Sends messages and images to Telegram using bot token and chat ID.
<PERSON><PERSON> errors gracefully and provides logging for debugging.

Functions:
- send_telegram(message, image_path): Send message with optional image
- send_message_only(message): Send text message only
- send_image_only(image_path, caption): Send image with caption

Configuration loaded from config.py
"""

import requests
import os
import logging
import sys
from typing import Optional
from datetime import datetime

# Configure stdout for Windows
sys.stdout.reconfigure(encoding='utf-8')

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_bot.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


def _escape_markdown(text: str) -> str:
    """
    Escape special characters for Telegram MarkdownV2 format.
    
    Args:
        text: Text to escape
        
    Returns:
        str: Escaped text safe for MarkdownV2
    """
    special_chars = ['_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']
    for char in special_chars:
        text = text.replace(char, f'\\{char}')
    return text


def send_telegram(message: str, image_path: Optional[str] = None) -> bool:
    """
    Send a message (with optional image) to Telegram chat.

    Args:
        message: Text message to send
        image_path: Optional path to image file (PNG, JPG, etc.)

    Returns:
        bool: True if sent successfully, False otherwise

    Example:
        >>> send_telegram("🚀 BUY Signal: BTCUSDT at $50,000")
        True

        >>> send_telegram("📊 Chart Analysis", "chart.png")
        True
    """
    try:
        # Import config here to avoid circular imports
        from config import get_config, get_telegram_config

        # Get Telegram configuration
        telegram_config = get_telegram_config()
        bot_token = telegram_config.get('telegram_token')
        chat_id = telegram_config.get('telegram_chat_id')

        # Validate configuration with detailed warnings
        if not bot_token:
            logger.error("❌ Telegram bot token is None - check config.py")
            print("❌ Telegram bot token not configured in config.py")
            return False

        if bot_token == 'your_telegram_bot_token':
            logger.error("❌ Telegram bot token not configured in config.py")
            print("❌ Telegram bot token not configured in config.py")
            print("💡 Set TELEGRAM_BOT_TOKEN environment variable or update config.py")
            return False

        if not chat_id:
            logger.error("❌ Telegram chat ID is None - check config.py")
            print("❌ Telegram chat ID not configured in config.py")
            return False

        if chat_id == 'your_telegram_chat_id':
            logger.error("❌ Telegram chat ID not configured in config.py")
            print("❌ Telegram chat ID not configured in config.py")
            print("💡 Set TELEGRAM_CHAT_ID environment variable or update config.py")
            return False

        # Escape message for MarkdownV2
        escaped_message = _escape_markdown(message)

        # Send message with or without image
        if image_path and os.path.exists(image_path):
            return _send_photo_with_caption(bot_token, chat_id, image_path, escaped_message)
        else:
            if image_path:
                logger.warning(f"⚠️ Image file not found: {image_path}, sending text only")
            return _send_text_message(bot_token, chat_id, escaped_message)

    except Exception as e:
        logger.error(f"💥 Error in send_telegram: {e}")
        return False


def send_message_only(message: str) -> bool:
    """
    Send text message only to Telegram.

    Args:
        message: Text message to send

    Returns:
        bool: True if sent successfully
    """
    return send_telegram(message, None)


def send_image_only(image_path: str, caption: str = "") -> bool:
    """
    Send image with caption to Telegram.

    Args:
        image_path: Path to image file
        caption: Image caption (optional)

    Returns:
        bool: True if sent successfully
    """
    if not caption:
        caption = f"📊 Chart - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

    return send_telegram(caption, image_path)


def _send_text_message(bot_token: str, chat_id: str, message: str) -> bool:
    """
    Send text message to Telegram.

    Args:
        bot_token: Telegram bot token
        chat_id: Telegram chat ID
        message: Message text

    Returns:
        bool: True if successful
    """
    try:
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"

        payload = {
            'chat_id': chat_id,
            'text': message,
            'parse_mode': 'MarkdownV2',
            'disable_web_page_preview': True
        }
        print(payload)

        response = requests.post(url, json=payload, timeout=10)

        if response.status_code == 200:
            logger.info("✅ Telegram message sent successfully")
            return True
        else:
            error_data = response.json() if response.content else {}
            error_description = error_data.get('description', 'Unknown error')
            logger.error(f"❌ Telegram API error: {response.status_code} - {error_description}")
            
            # Try sending without parse mode if parsing failed
            if response.status_code == 400 and 'parse_mode' in error_description:
                logger.warning("⚠️ Retrying without parse mode...")
                payload.pop('parse_mode')
                response = requests.post(url, json=payload, timeout=10)
                if response.status_code == 200:
                    logger.info("✅ Telegram message sent successfully (without parse mode)")
                    return True
            
            return False

    except requests.exceptions.Timeout:
        logger.error("⏰ Telegram request timeout (10s)")
        return False
    except requests.exceptions.ConnectionError:
        logger.error("🌐 Telegram connection error - check internet connection")
        return False
    except requests.exceptions.RequestException as e:
        logger.error(f"📡 Telegram request error: {e}")
        return False
    except Exception as e:
        logger.error(f"💥 Unexpected error sending Telegram message: {e}")
        return False

def _send_photo_with_caption(bot_token: str, chat_id: str, image_path: str, caption: str) -> bool:
    """
    Send photo with caption to Telegram.

    Args:
        bot_token: Telegram bot token
        chat_id: Telegram chat ID
        image_path: Path to image file
        caption: Photo caption

    Returns:
        bool: True if successful
    """
    try:
        url = f"https://api.telegram.org/bot{bot_token}/sendPhoto"

        # Validate image file
        if not os.path.exists(image_path):
            logger.error(f"📁 Image file not found: {image_path}")
            return False

        # Check file size (Telegram limit is 10MB for photos)
        file_size = os.path.getsize(image_path)
        if file_size > 10 * 1024 * 1024:  # 10MB
            logger.error(f"📏 Image file too large: {file_size / 1024 / 1024:.1f}MB (max 10MB)")
            return False

        # Prepare files and data
        with open(image_path, 'rb') as image_file:
            files = {
                'photo': ('chart.png', image_file, 'image/png')
            }

            data = {
                'chat_id': chat_id,
                'caption': caption,
                'parse_mode': 'MarkdownV2'
            }

            response = requests.post(url, files=files, data=data, timeout=30)

        if response.status_code == 200:
            logger.info(f"✅ Telegram photo sent successfully: {os.path.basename(image_path)}")
            return True
        else:
            error_data = response.json() if response.content else {}
            error_description = error_data.get('description', 'Unknown error')
            logger.error(f"❌ Telegram photo API error: {response.status_code} - {error_description}")
            
            # Try sending without parse mode if parsing failed
            if response.status_code == 400 and 'parse_mode' in error_description:
                logger.warning("⚠️ Retrying without parse mode...")
                data.pop('parse_mode')
                response = requests.post(url, files=files, data=data, timeout=30)
                if response.status_code == 200:
                    logger.info("✅ Telegram photo sent successfully (without parse mode)")
                    return True
            
            return False

    except requests.exceptions.Timeout:
        logger.error("⏰ Telegram photo request timeout (30s)")
        return False
    except requests.exceptions.ConnectionError:
        logger.error("🌐 Telegram photo connection error - check internet connection")
        return False
    except requests.exceptions.RequestException as e:
        logger.error(f"📡 Telegram photo request error: {e}")
        return False
    except FileNotFoundError:
        logger.error(f"📁 Image file not found: {image_path}")
        return False
    except Exception as e:
        logger.error(f"💥 Unexpected error sending Telegram photo: {e}")
        return False


def test_telegram_connection() -> bool:
    """
    Test Telegram connection by sending a test message.

    Returns:
        bool: True if connection successful
    """
    test_message = f"""
🤖 **Telegram Connection Test**

✅ Bot is connected and working!
⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🔧 This is a test message from the trading bot.
    """.strip()

    success = send_telegram(test_message)

    if success:
        logger.info("✅ Telegram connection test successful")
    else:
        logger.error("❌ Telegram connection test failed")

    return success


def format_trade_notification(trade_data: dict) -> str:
    """
    Format trade data into a nice Telegram message.

    Args:
        trade_data: Trade data dictionary

    Returns:
        str: Formatted message
    """
    action = trade_data.get('action', 'UNKNOWN')
    symbol = trade_data.get('symbol', 'UNKNOWN')
    entry_price = trade_data.get('entry_price', 0)
    stop_loss = trade_data.get('stop_loss', 0)
    take_profit = trade_data.get('take_profit', 0)
    confidence = trade_data.get('confidence', 0)
    strategy = trade_data.get('strategy', 'unknown')
    reason = trade_data.get('reason', 'No reason provided')
    position_size = trade_data.get('position_size_usd', 0)
    quantity = trade_data.get('quantity', 0)
    risk_amount = trade_data.get('risk_amount', 0)

    # Choose emoji based on action
    action_emoji = "🟢" if action == "BUY" else "🔴"
    confidence_emoji = "🔥" if confidence > 0.8 else "⚡" if confidence > 0.6 else "💡"

    # Calculate risk/reward ratio
    risk = abs(entry_price - stop_loss) if stop_loss else 0
    reward = abs(take_profit - entry_price) if take_profit else 0
    rr_ratio = reward / risk if risk > 0 else 0

    message = f"""
{action_emoji} **TRADING SIGNAL** {action_emoji}

{confidence_emoji} **Strategy:** {strategy}
📊 **Symbol:** {symbol}
🎯 **Action:** {action}

💰 **Entry Price:** ${entry_price:,.4f}
🛑 **Stop Loss:** ${stop_loss:,.4f}
🎯 **Take Profit:** ${take_profit:,.4f}
📈 **Risk/Reward:** 1:{rr_ratio:.2f}

💵 **Position Size:** ${position_size:,.2f}
📦 **Quantity:** {quantity:.6f}
⚠️ **Risk Amount:** ${risk_amount:,.2f}

🎲 **Confidence:** {confidence:.1%}
💭 **Reason:** {reason}

⏱ **Time:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    """.strip()

    return message


def format_error_notification(error_message: str, context: str = "") -> str:
    """
    Format error message for Telegram notification.

    Args:
        error_message: Error description
        context: Additional context (optional)

    Returns:
        str: Formatted error message
    """
    message = f"""
🚨 **TRADING BOT ERROR** 🚨

❌ **Error:** {error_message}

⏱ **Time:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    """

    if context:
        message += f"\n🔍 **Context:** {context}"

    message += "\n\n🔧 Please check the bot logs for more details."

    return message.strip()


def format_startup_notification() -> str:
    """
    Format startup notification message.

    Returns:
        str: Formatted startup message
    """
    return f"""
🚀 **TRADING BOT STARTED** 🚀

✅ Bot is now running and monitoring markets
⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🔍 Strategies loaded and active
📊 Market analysis in progress

💡 You will receive notifications for:
• Trading signals
• Trade executions
• Errors and warnings
• Daily summaries
    """.strip()


def format_shutdown_notification() -> str:
    """
    Format shutdown notification message.

    Returns:
        str: Formatted shutdown message
    """
    return f"""
🛑 **TRADING BOT STOPPED** 🛑

⏹ Bot has been shut down
⏰ Stopped at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📊 All monitoring has ceased

🔧 To restart the bot, run the main script again.
    """.strip()


class NotificationManager:
    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def send_custom_message(self, message: str) -> bool:
        return send_telegram(message, None)


# Example usage and testing
if __name__ == "__main__":
    # Test the notification system
    print("Testing Telegram notification system...")

    # Test connection
    print("\n1. Testing connection...")
    test_telegram_connection()

    # Test text message
    print("\n2. Testing text message...")
    test_message = "🧪 Test message from notify.py"
    send_telegram(test_message)

    # Test formatted trade notification
    print("\n3. Testing formatted trade notification...")
    sample_trade = {
        'action': 'BUY',
        'symbol': 'BTCUSDT',
        'entry_price': 50000.0,
        'stop_loss': 49000.0,
        'take_profit': 52000.0,
        'confidence': 0.85,
        'strategy': 'rsi_divergence',
        'reason': 'Bullish RSI divergence detected',
        'position_size_usd': 200.0,
        'quantity': 0.004,
        'risk_amount': 200.0
    }

    formatted_message = format_trade_notification(sample_trade)
    send_telegram(formatted_message)

    print("\n✅ Telegram notification tests completed!")
    print("Check your Telegram chat for the test messages.")
