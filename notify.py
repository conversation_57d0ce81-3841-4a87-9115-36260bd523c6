"""
Notification module for sending trade details and chart images to Telegram.
"""

import requests
import io
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from PIL import Image
import base64

from trade import TradeRecord
from strategies.base_strategy import TradingSignal


class TelegramNotifier:
    """
    Handles Telegram notifications for trading activities.
    """
    
    def __init__(self, bot_token: str, chat_id: str):
        """
        Initialize Telegram notifier.
        
        Args:
            bot_token: Telegram bot token
            chat_id: Telegram chat ID to send messages to
        """
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.base_url = f"https://api.telegram.org/bot{bot_token}"
        self.logger = logging.getLogger(__name__)
    
    def send_message(self, message: str, parse_mode: str = 'Markdown') -> bool:
        """
        Send a text message to Telegram.
        
        Args:
            message: Message text to send
            parse_mode: Message parsing mode ('Markdown' or 'HTML')
            
        Returns:
            bool: True if message was sent successfully
        """
        try:
            url = f"{self.base_url}/sendMessage"
            payload = {
                'chat_id': self.chat_id,
                'text': message,
                'parse_mode': parse_mode
            }
            
            response = requests.post(url, json=payload, timeout=10)
            response.raise_for_status()
            
            self.logger.info("Telegram message sent successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending Telegram message: {e}")
            return False
    
    def send_photo(self, image_data: bytes, caption: str = '') -> bool:
        """
        Send a photo to Telegram.
        
        Args:
            image_data: Image data as bytes
            caption: Photo caption
            
        Returns:
            bool: True if photo was sent successfully
        """
        try:
            url = f"{self.base_url}/sendPhoto"
            
            files = {'photo': ('chart.png', image_data, 'image/png')}
            data = {
                'chat_id': self.chat_id,
                'caption': caption,
                'parse_mode': 'Markdown'
            }
            
            response = requests.post(url, files=files, data=data, timeout=30)
            response.raise_for_status()
            
            self.logger.info("Telegram photo sent successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending Telegram photo: {e}")
            return False


class ChartGenerator:
    """
    Generates trading charts for notifications.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize chart generator.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def generate_trade_chart(self, trade_record: TradeRecord, market_data: Dict[str, Any]) -> bytes:
        """
        Generate a chart showing the trade entry/exit points.
        
        Args:
            trade_record: Trade record
            market_data: Market data for the chart
            
        Returns:
            bytes: Chart image as bytes
        """
        try:
            # TODO: Implement chart generation
            # 1. Create candlestick chart
            # 2. Add entry/exit markers
            # 3. Add stop loss and take profit lines
            # 4. Add technical indicators
            # 5. Add trade information overlay
            
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # Placeholder chart
            ax.plot([1, 2, 3, 4, 5], [100, 105, 102, 108, 106])
            ax.set_title(f"{trade_record.symbol} - {trade_record.strategy}")
            ax.set_xlabel("Time")
            ax.set_ylabel("Price")
            
            # Add trade markers
            ax.axhline(y=trade_record.entry_price, color='blue', linestyle='--', label='Entry')
            if trade_record.stop_loss:
                ax.axhline(y=trade_record.stop_loss, color='red', linestyle='--', label='Stop Loss')
            if trade_record.take_profit:
                ax.axhline(y=trade_record.take_profit, color='green', linestyle='--', label='Take Profit')
            
            ax.legend()
            
            # Save to bytes
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            image_data = buffer.getvalue()
            buffer.close()
            plt.close(fig)
            
            return image_data
            
        except Exception as e:
            self.logger.error(f"Error generating trade chart: {e}")
            return b''
    
    def generate_signal_chart(self, signal: TradingSignal, market_data: Dict[str, Any]) -> bytes:
        """
        Generate a chart showing the trading signal.
        
        Args:
            signal: Trading signal
            market_data: Market data for the chart
            
        Returns:
            bytes: Chart image as bytes
        """
        try:
            # TODO: Implement signal chart generation
            # 1. Create candlestick chart
            # 2. Add signal marker
            # 3. Add relevant indicators
            # 4. Add signal information overlay
            
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # Placeholder chart
            ax.plot([1, 2, 3, 4, 5], [100, 105, 102, 108, 106])
            ax.set_title(f"{signal.symbol} - {signal.metadata.get('strategy', 'Unknown')} Signal")
            ax.set_xlabel("Time")
            ax.set_ylabel("Price")
            
            # Add signal marker
            ax.scatter([3], [signal.price], color='red' if signal.action == 'SELL' else 'green', 
                      s=100, label=f'{signal.action} Signal')
            
            ax.legend()
            
            # Save to bytes
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            image_data = buffer.getvalue()
            buffer.close()
            plt.close(fig)
            
            return image_data
            
        except Exception as e:
            self.logger.error(f"Error generating signal chart: {e}")
            return b''


class MessageFormatter:
    """
    Formats messages for different types of notifications.
    """
    
    @staticmethod
    def format_trade_signal(signal: TradingSignal) -> str:
        """
        Format a trading signal for notification.
        
        Args:
            signal: Trading signal
            
        Returns:
            str: Formatted message
        """
        strategy = signal.metadata.get('strategy', 'Unknown')
        pattern = signal.metadata.get('pattern', 'N/A')
        
        message = f"""
🚨 *TRADING SIGNAL* 🚨

📊 *Symbol:* {signal.symbol}
🎯 *Action:* {signal.action}
💰 *Price:* ${signal.price:.4f}
📈 *Strategy:* {strategy}
🔍 *Pattern:* {pattern}
⏰ *Timeframe:* {signal.timeframe}
🎲 *Confidence:* {signal.confidence:.1%}

⏱ *Time:* {signal.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
        """.strip()
        
        if signal.stop_loss:
            message += f"\n🛑 *Stop Loss:* ${signal.stop_loss:.4f}"
        
        if signal.take_profit:
            message += f"\n🎯 *Take Profit:* ${signal.take_profit:.4f}"
        
        return message
    
    @staticmethod
    def format_trade_execution(trade_record: TradeRecord) -> str:
        """
        Format a trade execution for notification.
        
        Args:
            trade_record: Trade record
            
        Returns:
            str: Formatted message
        """
        message = f"""
✅ *TRADE EXECUTED* ✅

📊 *Symbol:* {trade_record.symbol}
🎯 *Action:* {trade_record.action}
💰 *Entry Price:* ${trade_record.entry_price:.4f}
📦 *Quantity:* {trade_record.quantity:.6f}
💵 *Position Value:* ${trade_record.entry_price * trade_record.quantity:.2f}
📈 *Strategy:* {trade_record.strategy}
⏰ *Timeframe:* {trade_record.timeframe}
🎲 *Confidence:* {trade_record.confidence:.1%}

🛑 *Stop Loss:* ${trade_record.stop_loss:.4f}
🎯 *Take Profit:* ${trade_record.take_profit:.4f}

⏱ *Time:* {trade_record.entry_time.strftime('%Y-%m-%d %H:%M:%S')}
🆔 *Trade ID:* {trade_record.trade_id}
        """.strip()
        
        return message
    
    @staticmethod
    def format_trade_closure(trade_record: TradeRecord) -> str:
        """
        Format a trade closure for notification.
        
        Args:
            trade_record: Trade record
            
        Returns:
            str: Formatted message
        """
        pnl_emoji = "💚" if trade_record.pnl > 0 else "❤️"
        status_emoji = "✅" if trade_record.pnl > 0 else "❌"
        
        message = f"""
{status_emoji} *TRADE CLOSED* {status_emoji}

📊 *Symbol:* {trade_record.symbol}
🎯 *Action:* {trade_record.action}
💰 *Entry Price:* ${trade_record.entry_price:.4f}
💰 *Exit Price:* ${trade_record.exit_price:.4f}
📦 *Quantity:* {trade_record.quantity:.6f}

{pnl_emoji} *P&L:* ${trade_record.pnl:.2f} ({trade_record.pnl_percentage:.2f}%)
💸 *Fees:* ${trade_record.fees:.2f}

📈 *Strategy:* {trade_record.strategy}
⏰ *Duration:* {(trade_record.exit_time - trade_record.entry_time).total_seconds() / 3600:.1f}h

⏱ *Entry:* {trade_record.entry_time.strftime('%Y-%m-%d %H:%M:%S')}
⏱ *Exit:* {trade_record.exit_time.strftime('%Y-%m-%d %H:%M:%S')}
🆔 *Trade ID:* {trade_record.trade_id}
        """.strip()
        
        return message
    
    @staticmethod
    def format_daily_summary(trades: List[TradeRecord], account_balance: float) -> str:
        """
        Format daily trading summary.
        
        Args:
            trades: List of trades for the day
            account_balance: Current account balance
            
        Returns:
            str: Formatted message
        """
        total_pnl = sum(trade.pnl for trade in trades if trade.pnl)
        winning_trades = len([t for t in trades if t.pnl and t.pnl > 0])
        losing_trades = len([t for t in trades if t.pnl and t.pnl < 0])
        win_rate = (winning_trades / len(trades)) * 100 if trades else 0
        
        message = f"""
📊 *DAILY TRADING SUMMARY* 📊

💰 *Account Balance:* ${account_balance:.2f}
📈 *Total P&L:* ${total_pnl:.2f}
🔢 *Total Trades:* {len(trades)}
✅ *Winning Trades:* {winning_trades}
❌ *Losing Trades:* {losing_trades}
🎯 *Win Rate:* {win_rate:.1f}%

⏱ *Date:* {datetime.now().strftime('%Y-%m-%d')}
        """.strip()
        
        return message


class NotificationManager:
    """
    Main notification manager that orchestrates all notification activities.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize notification manager.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.telegram_notifier = None
        self.chart_generator = ChartGenerator(config)
        self.logger = logging.getLogger(__name__)
        
        # Initialize Telegram if credentials are provided
        if config.get('telegram_token') and config.get('telegram_chat_id'):
            self.telegram_notifier = TelegramNotifier(
                config['telegram_token'],
                config['telegram_chat_id']
            )
    
    def send_signal_notification(self, signal: TradingSignal, market_data: Dict[str, Any] = None):
        """
        Send notification for a new trading signal.
        
        Args:
            signal: Trading signal
            market_data: Market data for chart generation
        """
        if not self.telegram_notifier:
            return
        
        try:
            # Send text message
            message = MessageFormatter.format_trade_signal(signal)
            self.telegram_notifier.send_message(message)
            
            # Send chart if market data is available
            if market_data and self.config.get('send_charts', True):
                chart_data = self.chart_generator.generate_signal_chart(signal, market_data)
                if chart_data:
                    caption = f"{signal.symbol} - {signal.action} Signal"
                    self.telegram_notifier.send_photo(chart_data, caption)
                    
        except Exception as e:
            self.logger.error(f"Error sending signal notification: {e}")
    
    def send_trade_notification(self, trade_record: TradeRecord, strategy_name: str, 
                              market_data: Dict[str, Any] = None):
        """
        Send notification for trade execution.
        
        Args:
            trade_record: Trade record
            strategy_name: Name of the strategy
            market_data: Market data for chart generation
        """
        if not self.telegram_notifier:
            return
        
        try:
            # Send text message
            message = MessageFormatter.format_trade_execution(trade_record)
            self.telegram_notifier.send_message(message)
            
            # Send chart if market data is available
            if market_data and self.config.get('send_charts', True):
                chart_data = self.chart_generator.generate_trade_chart(trade_record, market_data)
                if chart_data:
                    caption = f"{trade_record.symbol} - Trade Executed"
                    self.telegram_notifier.send_photo(chart_data, caption)
                    
        except Exception as e:
            self.logger.error(f"Error sending trade notification: {e}")
    
    def send_trade_closure_notification(self, trade_record: TradeRecord):
        """
        Send notification for trade closure.
        
        Args:
            trade_record: Closed trade record
        """
        if not self.telegram_notifier:
            return
        
        try:
            message = MessageFormatter.format_trade_closure(trade_record)
            self.telegram_notifier.send_message(message)
            
        except Exception as e:
            self.logger.error(f"Error sending trade closure notification: {e}")
    
    def send_daily_summary(self, trades: List[TradeRecord], account_balance: float):
        """
        Send daily trading summary.
        
        Args:
            trades: List of trades for the day
            account_balance: Current account balance
        """
        if not self.telegram_notifier:
            return
        
        try:
            message = MessageFormatter.format_daily_summary(trades, account_balance)
            self.telegram_notifier.send_message(message)
            
        except Exception as e:
            self.logger.error(f"Error sending daily summary: {e}")
    
    def send_custom_message(self, message: str):
        """
        Send a custom message.
        
        Args:
            message: Custom message to send
        """
        if not self.telegram_notifier:
            return
        
        self.telegram_notifier.send_message(message)
