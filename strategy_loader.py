"""
Strategy loader module for dynamically loading enabled trading strategies.
"""

import importlib
import os
from typing import List, Dict, Any
import logging

from strategy_control import StrategyControl


class StrategyLoader:
    """
    Dynamically loads and manages trading strategies based on control flags.
    """
    
    def __init__(self, strategies_path: str = "strategies"):
        """
        Initialize the strategy loader.
        
        Args:
            strategies_path: Path to the strategies directory
        """
        self.strategies_path = strategies_path
        self.loaded_strategies = {}
        self.logger = logging.getLogger(__name__)
    
    def load_enabled_strategies(self) -> List[Any]:
        """
        Load all enabled strategies from the strategies directory.
        
        Returns:
            list: List of instantiated strategy objects
        """
        enabled_strategies = []
        enabled_strategy_names = StrategyControl.get_enabled_strategies()
        
        for strategy_name in enabled_strategy_names:
            try:
                strategy_instance = self._load_strategy(strategy_name)
                if strategy_instance:
                    enabled_strategies.append(strategy_instance)
                    self.logger.info(f"Loaded strategy: {strategy_name}")
            except Exception as e:
                self.logger.error(f"Failed to load strategy {strategy_name}: {e}")
        
        return enabled_strategies
    
    def _load_strategy(self, strategy_name: str) -> Any:
        """
        Load a specific strategy by name.
        
        Args:
            strategy_name: Name of the strategy to load
            
        Returns:
            Strategy instance or None if loading failed
        """
        # Check if strategy is already loaded
        if strategy_name in self.loaded_strategies:
            return self.loaded_strategies[strategy_name]
        
        try:
            # Import the strategy module
            module_name = f"{self.strategies_path}.{strategy_name}"
            strategy_module = importlib.import_module(module_name)
            
            # Get the strategy class (assuming class name is CamelCase version of file name)
            class_name = self._snake_to_camel(strategy_name)
            strategy_class = getattr(strategy_module, class_name)
            
            # Get strategy parameters
            params = StrategyControl.get_strategy_params(strategy_name)
            
            # Instantiate the strategy
            strategy_instance = strategy_class(params)
            
            # Cache the loaded strategy
            self.loaded_strategies[strategy_name] = strategy_instance
            
            return strategy_instance
            
        except ImportError as e:
            self.logger.error(f"Could not import strategy module {strategy_name}: {e}")
            return None
        except AttributeError as e:
            self.logger.error(f"Strategy class not found in {strategy_name}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error loading strategy {strategy_name}: {e}")
            return None
    
    def _snake_to_camel(self, snake_str: str) -> str:
        """
        Convert snake_case to CamelCase.
        
        Args:
            snake_str: String in snake_case format
            
        Returns:
            str: String in CamelCase format
        """
        components = snake_str.split('_')
        return ''.join(word.capitalize() for word in components)
    
    def reload_strategy(self, strategy_name: str) -> Any:
        """
        Reload a specific strategy (useful for development).
        
        Args:
            strategy_name: Name of the strategy to reload
            
        Returns:
            Reloaded strategy instance
        """
        # Remove from cache
        if strategy_name in self.loaded_strategies:
            del self.loaded_strategies[strategy_name]
        
        # Reload the module
        module_name = f"{self.strategies_path}.{strategy_name}"
        if module_name in importlib.sys.modules:
            importlib.reload(importlib.sys.modules[module_name])
        
        # Load the strategy again
        return self._load_strategy(strategy_name)
    
    def get_available_strategies(self) -> List[str]:
        """
        Get list of all available strategy files in the strategies directory.
        
        Returns:
            list: List of available strategy names
        """
        available_strategies = []
        
        if not os.path.exists(self.strategies_path):
            self.logger.warning(f"Strategies directory {self.strategies_path} does not exist")
            return available_strategies
        
        for filename in os.listdir(self.strategies_path):
            if filename.endswith('.py') and not filename.startswith('__'):
                strategy_name = filename[:-3]  # Remove .py extension
                available_strategies.append(strategy_name)
        
        return available_strategies
    
    def validate_strategies(self) -> Dict[str, bool]:
        """
        Validate that all enabled strategies can be loaded.
        
        Returns:
            dict: Dictionary mapping strategy names to validation status
        """
        validation_results = {}
        enabled_strategies = StrategyControl.get_enabled_strategies()
        
        for strategy_name in enabled_strategies:
            try:
                strategy_instance = self._load_strategy(strategy_name)
                validation_results[strategy_name] = strategy_instance is not None
            except Exception as e:
                self.logger.error(f"Validation failed for {strategy_name}: {e}")
                validation_results[strategy_name] = False
        
        return validation_results
    
    def clear_cache(self):
        """Clear the strategy cache."""
        self.loaded_strategies.clear()
        self.logger.info("Strategy cache cleared")
