"""
Strategy Loader Module

Dynamically scans the strategies/ folder and imports enabled strategy modules.
Each strategy module must have a run() function that accepts parameters.

Usage:
    loader = StrategyLoader()
    strategies = loader.load_enabled_strategies()

    for strategy in strategies:
        results = strategy.run(symbol="BTCUSDT", timeframe="1h", market_data=data)
"""

import importlib
import os
import sys
from typing import List, Dict, Any, Optional
import logging
import traceback

from strategy_control import StrategyControl


class StrategyModule:
    """
    Wrapper class for loaded strategy modules.
    Provides a consistent interface for all strategies.
    """

    def __init__(self, module, name: str, params: Dict[str, Any]):
        """
        Initialize strategy module wrapper.

        Args:
            module: The imported Python module
            name: Strategy name
            params: Strategy parameters
        """
        self.module = module
        self.name = name
        self.params = params
        self.is_valid = self._validate_module()

    def _validate_module(self) -> bool:
        """
        Validate that the module has required functions.

        Returns:
            bool: True if module is valid
        """
        required_functions = ['run']

        for func_name in required_functions:
            if not hasattr(self.module, func_name):
                logging.getLogger(__name__).warning(
                    f"Strategy {self.name} missing required function: {func_name}"
                )
                return False

        return True

    def run(self, symbol: str, timeframe: str, market_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Execute the strategy's run function.

        Args:
            symbol: Trading symbol (e.g., "BTCUSDT")
            timeframe: Timeframe (e.g., "1h", "4h")
            market_data: Market data dictionary
            **kwargs: Additional parameters

        Returns:
            dict: Strategy execution results
        """
        if not self.is_valid:
            return {
                'error': f'Strategy {self.name} is not valid',
                'signals': [],
                'status': 'invalid'
            }

        try:
            # Call the strategy's run function
            result = self.module.run(
                symbol=symbol,
                timeframe=timeframe,
                market_data=market_data,
                params=self.params,
                **kwargs
            )

            # Ensure result is in expected format
            if not isinstance(result, dict):
                return {
                    'error': f'Strategy {self.name} returned invalid result type',
                    'signals': [],
                    'status': 'error'
                }

            # Add metadata
            result['strategy_name'] = self.name
            result['status'] = result.get('status', 'success')

            return result

        except Exception as e:
            logging.getLogger(__name__).error(
                f"Error executing strategy {self.name}: {e}\n{traceback.format_exc()}"
            )
            return {
                'error': str(e),
                'signals': [],
                'status': 'error',
                'strategy_name': self.name
            }

    def get_info(self) -> Dict[str, Any]:
        """
        Get strategy information.

        Returns:
            dict: Strategy information
        """
        info = {
            'name': self.name,
            'is_valid': self.is_valid,
            'params': self.params
        }

        # Try to get additional info from module
        if hasattr(self.module, 'get_strategy_info'):
            try:
                module_info = self.module.get_strategy_info()
                info.update(module_info)
            except Exception as e:
                logging.getLogger(__name__).warning(
                    f"Could not get info from strategy {self.name}: {e}"
                )

        return info


class StrategyLoader:
    """
    Dynamically loads and manages trading strategies from the strategies/ folder.
    """

    def __init__(self, strategies_path: str = "strategies"):
        """
        Initialize the strategy loader.

        Args:
            strategies_path: Path to the strategies directory
        """
        self.strategies_path = strategies_path
        self.loaded_modules = {}
        self.logger = logging.getLogger(__name__)

        # Ensure strategies path is in Python path
        if strategies_path not in sys.path:
            sys.path.insert(0, os.path.dirname(os.path.abspath(strategies_path)))

    def scan_strategies_folder(self) -> List[str]:
        """
        Scan the strategies folder for Python files.

        Returns:
            list: List of strategy filenames (without .py extension)
        """
        available_strategies = []

        if not os.path.exists(self.strategies_path):
            self.logger.error(f"Strategies directory '{self.strategies_path}' does not exist")
            return available_strategies

        try:
            for filename in os.listdir(self.strategies_path):
                if (filename.endswith('.py') and
                    not filename.startswith('__') and
                    filename != 'base_strategy.py'):

                    strategy_name = filename[:-3]  # Remove .py extension
                    available_strategies.append(strategy_name)

            self.logger.info(f"Found {len(available_strategies)} strategy files: {available_strategies}")

        except Exception as e:
            self.logger.error(f"Error scanning strategies folder: {e}")

        return available_strategies

    def load_enabled_strategies(self) -> List[StrategyModule]:
        """
        Load all enabled strategies from the strategies directory.

        Returns:
            list: List of StrategyModule objects for enabled strategies
        """
        enabled_strategies = []

        # Get available strategy files
        available_strategies = self.scan_strategies_folder()

        # Get enabled strategies from control
        enabled_strategy_names = StrategyControl.get_enabled_strategies()

        self.logger.info(f"Enabled strategies in control: {enabled_strategy_names}")

        for strategy_name in enabled_strategy_names:
            # Check if strategy file exists
            if strategy_name not in available_strategies:
                self.logger.warning(
                    f"Strategy '{strategy_name}' is enabled but file '{strategy_name}.py' not found in {self.strategies_path}/"
                )
                continue

            # Load the strategy
            try:
                strategy_module = self._load_strategy_module(strategy_name)
                if strategy_module:
                    enabled_strategies.append(strategy_module)
                    self.logger.info(f"Successfully loaded strategy: {strategy_name}")
                else:
                    self.logger.error(f"Failed to load strategy: {strategy_name}")

            except Exception as e:
                self.logger.error(f"Error loading strategy {strategy_name}: {e}")

        self.logger.info(f"Loaded {len(enabled_strategies)} strategies successfully")
        return enabled_strategies

    def _load_strategy_module(self, strategy_name: str) -> Optional[StrategyModule]:
        """
        Load a specific strategy module.

        Args:
            strategy_name: Name of the strategy to load

        Returns:
            StrategyModule or None if loading failed
        """
        # Check cache first
        if strategy_name in self.loaded_modules:
            self.logger.debug(f"Using cached strategy: {strategy_name}")
            return self.loaded_modules[strategy_name]

        try:
            # Import the strategy module
            module_name = f"{self.strategies_path}.{strategy_name}"

            # Remove from sys.modules if already imported (for reloading)
            if module_name in sys.modules:
                del sys.modules[module_name]

            # Import the module
            strategy_module = importlib.import_module(module_name)

            # Get strategy parameters
            params = StrategyControl.get_strategy_params(strategy_name)

            # Create strategy wrapper
            strategy_wrapper = StrategyModule(strategy_module, strategy_name, params)

            # Cache the loaded strategy
            self.loaded_modules[strategy_name] = strategy_wrapper

            return strategy_wrapper

        except ImportError as e:
            self.logger.error(f"Could not import strategy module '{strategy_name}': {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error loading strategy '{strategy_name}': {e}\n{traceback.format_exc()}")
            return None

    def reload_strategy(self, strategy_name: str) -> Optional[StrategyModule]:
        """
        Reload a specific strategy (useful for development).

        Args:
            strategy_name: Name of the strategy to reload

        Returns:
            StrategyModule or None if reloading failed
        """
        self.logger.info(f"Reloading strategy: {strategy_name}")

        # Remove from cache
        if strategy_name in self.loaded_modules:
            del self.loaded_modules[strategy_name]

        # Remove from sys.modules
        module_name = f"{self.strategies_path}.{strategy_name}"
        if module_name in sys.modules:
            del sys.modules[module_name]

        # Load the strategy again
        return self._load_strategy_module(strategy_name)

    def get_strategy_status(self) -> Dict[str, Dict[str, Any]]:
        """
        Get status of all strategies (available, enabled, loaded).

        Returns:
            dict: Dictionary with strategy status information
        """
        available_strategies = self.scan_strategies_folder()
        enabled_strategies = StrategyControl.get_enabled_strategies()

        status = {}

        for strategy_name in available_strategies:
            is_enabled = strategy_name in enabled_strategies
            is_loaded = strategy_name in self.loaded_modules

            status[strategy_name] = {
                'available': True,
                'enabled': is_enabled,
                'loaded': is_loaded,
                'valid': self.loaded_modules[strategy_name].is_valid if is_loaded else None
            }

        # Check for enabled strategies that don't have files
        for strategy_name in enabled_strategies:
            if strategy_name not in available_strategies:
                status[strategy_name] = {
                    'available': False,
                    'enabled': True,
                    'loaded': False,
                    'valid': None,
                    'error': 'File not found'
                }

        return status

    def validate_all_strategies(self) -> Dict[str, bool]:
        """
        Validate that all enabled strategies can be loaded and have required functions.

        Returns:
            dict: Dictionary mapping strategy names to validation status
        """
        validation_results = {}
        enabled_strategies = StrategyControl.get_enabled_strategies()

        for strategy_name in enabled_strategies:
            try:
                strategy_module = self._load_strategy_module(strategy_name)
                validation_results[strategy_name] = strategy_module is not None and strategy_module.is_valid
            except Exception as e:
                self.logger.error(f"Validation failed for {strategy_name}: {e}")
                validation_results[strategy_name] = False

        return validation_results

    def clear_cache(self):
        """Clear the strategy module cache."""
        self.loaded_modules.clear()
        self.logger.info("Strategy cache cleared")

    def get_loaded_strategies(self) -> List[StrategyModule]:
        """
        Get list of currently loaded strategy modules.

        Returns:
            list: List of loaded StrategyModule objects
        """
        return list(self.loaded_modules.values())


# Convenience functions for easy usage
def load_strategies() -> List[StrategyModule]:
    """
    Convenience function to load all enabled strategies.

    Returns:
        list: List of loaded strategy modules
    """
    loader = StrategyLoader()
    return loader.load_enabled_strategies()


def get_strategy_status() -> Dict[str, Dict[str, Any]]:
    """
    Convenience function to get strategy status.

    Returns:
        dict: Strategy status information
    """
    loader = StrategyLoader()
    return loader.get_strategy_status()
