"""
N8N API Integration Module

Sends trading signals to N8N webhook for AI agent analysis and approval.
The webhook triggers an automated workflow that analyzes the trade signal
and returns an approval/rejection decision.

Functions:
- send_to_n8n(trade): Send trade signal to N8N webhook
- format_n8n_payload(trade): Format trade data for N8N
- validate_n8n_response(response): Validate webhook response

Usage:
    from ai_agent.n8n_api import send_to_n8n
    
    trade_data = {...}  # Strategy output
    result = send_to_n8n(trade_data)
    if result['approved']:
        execute_trade(trade_data)
"""

import requests
import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime
import time

# Import config
try:
    import config
except ImportError:
    print("Warning: config.py not found. Using default configuration.")
    config = None


def send_to_n8n(trade: Dict[str, Any]) -> Dict[str, Any]:
    """
    Send a trading signal to N8N webhook for AI agent analysis.
    
    Sends a POST request with the trade signal data to a pre-configured
    N8N webhook URL. The webhook triggers an AI agent workflow that
    analyzes the signal and returns an approval/rejection decision.
    
    Args:
        trade: Dictionary containing trade signal data with keys:
            - 'action': 'BUY' or 'SELL'
            - 'price': Entry price (float)
            - 'stop_loss': Stop loss price (float)
            - 'take_profit': Take profit price (float)
            - 'confidence': Confidence score (0-1)
            - 'reason': Signal reasoning (string)
            - 'metadata': Additional data including strategy_name, symbol, timeframe
            
    Returns:
        dict: Response from N8N webhook containing:
            - 'success': bool - Whether request was successful
            - 'approved': bool - Whether AI agent approved the trade
            - 'confidence': float - AI confidence in decision (0-1)
            - 'reasoning': str - AI reasoning for decision
            - 'modifications': dict - Suggested modifications (if any)
            - 'response_time': float - Time taken for analysis
            - 'raw_response': dict - Full webhook response
            
    Example:
        >>> trade_data = {
        ...     'action': 'BUY',
        ...     'price': 50000.0,
        ...     'stop_loss': 49000.0,
        ...     'take_profit': 52000.0,
        ...     'confidence': 0.85,
        ...     'reason': 'Bullish RSI divergence detected',
        ...     'metadata': {
        ...         'strategy_name': 'rsi_divergence',
        ...         'symbol': 'BTCUSDT',
        ...         'timeframe': '1h'
        ...     }
        ... }
        >>> result = send_to_n8n(trade_data)
        >>> if result['approved']:
        ...     print("AI approved the trade!")
        >>> else:
        ...     print(f"AI rejected: {result['reasoning']}")
    """
    logger = logging.getLogger(__name__)
    start_time = time.time()
    
    try:
        # Get N8N webhook URL from config
        webhook_url = _get_webhook_url()
        if not webhook_url:
            return _create_error_response("N8N webhook URL not configured")
        
        # Format trade data for N8N
        payload = format_n8n_payload(trade)
        
        # Set request headers
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'TradingBot/1.0',
            'X-Request-Source': 'trading-bot',
            'X-Timestamp': datetime.now().isoformat()
        }
        
        # Add authentication if configured
        auth_token = _get_auth_token()
        if auth_token:
            headers['Authorization'] = f'Bearer {auth_token}'
        
        logger.info(f"Sending trade signal to N8N: {trade.get('metadata', {}).get('symbol', 'UNKNOWN')} {trade.get('action', 'UNKNOWN')}")
        
        # Send POST request to N8N webhook
        response = requests.post(
            webhook_url,
            json=payload,
            headers=headers,
            timeout=30,  # 30 second timeout
            verify=True  # Verify SSL certificates
        )
        
        # Calculate response time
        response_time = time.time() - start_time
        
        # Check if request was successful
        response.raise_for_status()
        
        # Parse response
        try:
            response_data = response.json()
        except json.JSONDecodeError:
            logger.error("Invalid JSON response from N8N webhook")
            return _create_error_response("Invalid JSON response from webhook", response_time)
        
        # Validate response format
        validated_response = validate_n8n_response(response_data)
        validated_response['response_time'] = response_time
        validated_response['raw_response'] = response_data
        
        # Log the result
        if validated_response['approved']:
            logger.info(f"✅ AI APPROVED trade: {validated_response['reasoning']} (Confidence: {validated_response['confidence']:.1%})")
        else:
            logger.warning(f"❌ AI REJECTED trade: {validated_response['reasoning']} (Confidence: {validated_response['confidence']:.1%})")
        
        # Print response for testing (as requested)
        print("\n" + "="*60)
        print("N8N WEBHOOK RESPONSE:")
        print("="*60)
        print(f"Status: {'✅ APPROVED' if validated_response['approved'] else '❌ REJECTED'}")
        print(f"Confidence: {validated_response['confidence']:.1%}")
        print(f"Reasoning: {validated_response['reasoning']}")
        print(f"Response Time: {response_time:.2f}s")
        
        if validated_response.get('modifications'):
            print(f"Suggested Modifications: {validated_response['modifications']}")
        
        print("Raw Response:")
        print(json.dumps(response_data, indent=2))
        print("="*60)
        
        return validated_response
        
    except requests.exceptions.Timeout:
        logger.error("N8N webhook request timed out")
        return _create_error_response("Request timed out", time.time() - start_time)
        
    except requests.exceptions.ConnectionError:
        logger.error("Failed to connect to N8N webhook")
        return _create_error_response("Connection failed", time.time() - start_time)
        
    except requests.exceptions.HTTPError as e:
        logger.error(f"HTTP error from N8N webhook: {e}")
        return _create_error_response(f"HTTP error: {e}", time.time() - start_time)
        
    except Exception as e:
        logger.error(f"Unexpected error sending to N8N: {e}")
        return _create_error_response(f"Unexpected error: {e}", time.time() - start_time)


def format_n8n_payload(trade: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format trade data into the payload structure expected by N8N webhook.
    
    Args:
        trade: Raw trade data dictionary
        
    Returns:
        dict: Formatted payload for N8N webhook
    """
    # Extract trade data with defaults
    action = trade.get('action', 'UNKNOWN').upper()
    entry_price = float(trade.get('price', 0.0))
    stop_loss = float(trade.get('stop_loss', 0.0))
    take_profit = float(trade.get('take_profit', 0.0))
    confidence = float(trade.get('confidence', 0.0))
    reason = str(trade.get('reason', 'No reason provided'))
    
    # Extract metadata
    metadata = trade.get('metadata', {})
    strategy_name = metadata.get('strategy_name', 'unknown_strategy')
    symbol = metadata.get('symbol', 'UNKNOWN')
    timeframe = metadata.get('timeframe', '1h')
    
    # Calculate risk/reward metrics
    risk_amount = abs(entry_price - stop_loss) if stop_loss else 0
    reward_amount = abs(take_profit - entry_price) if take_profit else 0
    risk_reward_ratio = reward_amount / risk_amount if risk_amount > 0 else 0
    risk_percent = (risk_amount / entry_price * 100) if entry_price > 0 else 0
    
    # Create structured payload
    payload = {
        'signal': {
            'strategy': strategy_name,
            'symbol': symbol,
            'timeframe': timeframe,
            'action': action,
            'entry_price': entry_price,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'confidence': confidence,
            'reason': reason
        },
        'risk_analysis': {
            'risk_amount': risk_amount,
            'reward_amount': reward_amount,
            'risk_reward_ratio': risk_reward_ratio,
            'risk_percent': risk_percent
        },
        'metadata': {
            'timestamp': datetime.now().isoformat(),
            'source': 'trading-bot',
            'version': '1.0',
            'request_id': f"{symbol}_{strategy_name}_{int(time.time())}"
        }
    }
    
    return payload


def validate_n8n_response(response: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate and normalize the response from N8N webhook.
    
    Args:
        response: Raw response from N8N webhook
        
    Returns:
        dict: Validated and normalized response
    """
    # Default response structure
    validated = {
        'success': True,
        'approved': False,
        'confidence': 0.0,
        'reasoning': 'No reasoning provided',
        'modifications': None
    }
    
    try:
        # Extract approval decision
        if 'approved' in response:
            validated['approved'] = bool(response['approved'])
        elif 'decision' in response:
            validated['approved'] = str(response['decision']).upper() in ['APPROVED', 'APPROVE', 'YES', 'TRUE']
        elif 'recommendation' in response:
            validated['approved'] = str(response['recommendation']).upper() == 'EXECUTE'
        
        # Extract confidence
        if 'confidence' in response:
            confidence = float(response['confidence'])
            # Normalize confidence to 0-1 range
            if confidence > 1:
                confidence = confidence / 100
            validated['confidence'] = max(0.0, min(1.0, confidence))
        
        # Extract reasoning
        if 'reasoning' in response:
            validated['reasoning'] = str(response['reasoning'])
        elif 'reason' in response:
            validated['reasoning'] = str(response['reason'])
        elif 'message' in response:
            validated['reasoning'] = str(response['message'])
        
        # Extract modifications
        if 'modifications' in response and response['modifications']:
            validated['modifications'] = response['modifications']
        elif 'suggestions' in response and response['suggestions']:
            validated['modifications'] = response['suggestions']
            
    except Exception as e:
        logging.getLogger(__name__).error(f"Error validating N8N response: {e}")
        validated['success'] = False
        validated['reasoning'] = f"Error parsing response: {e}"
    
    return validated


def _get_webhook_url() -> Optional[str]:
    """Get N8N webhook URL from config."""
    if not config:
        return None
    
    n8n_config = config.get_n8n_config() if hasattr(config, 'get_n8n_config') else {}
    return n8n_config.get('webhook_url') or n8n_config.get('n8n_webhook_url')


def _get_auth_token() -> Optional[str]:
    """Get authentication token from config."""
    if not config:
        return None
    
    n8n_config = config.get_n8n_config() if hasattr(config, 'get_n8n_config') else {}
    return n8n_config.get('auth_token') or n8n_config.get('api_key')


def _create_error_response(error_message: str, response_time: float = 0.0) -> Dict[str, Any]:
    """Create a standardized error response."""
    return {
        'success': False,
        'approved': False,
        'confidence': 0.0,
        'reasoning': error_message,
        'modifications': None,
        'response_time': response_time,
        'raw_response': None
    }


# Example usage and testing
if __name__ == "__main__":
    # Test the N8N API integration
    print("Testing N8N API integration...")
    
    # Sample trade data
    sample_trade = {
        'action': 'BUY',
        'price': 50000.0,
        'stop_loss': 49000.0,
        'take_profit': 52000.0,
        'confidence': 0.85,
        'reason': 'Bullish RSI divergence detected with strong volume confirmation',
        'metadata': {
            'strategy_name': 'rsi_divergence',
            'symbol': 'BTCUSDT',
            'timeframe': '1h'
        }
    }
    
    print("\n1. Testing payload formatting...")
    payload = format_n8n_payload(sample_trade)
    print("✅ Payload formatted successfully")
    print(f"Payload keys: {list(payload.keys())}")
    
    print("\n2. Testing response validation...")
    mock_response = {
        'approved': True,
        'confidence': 85,
        'reasoning': 'Strong technical setup with good risk/reward ratio'
    }
    validated = validate_n8n_response(mock_response)
    print("✅ Response validation successful")
    print(f"Validated response: {validated}")
    
    print("\n3. Testing N8N webhook call...")
    # This will fail if webhook URL is not configured, which is expected
    result = send_to_n8n(sample_trade)
    print(f"Result success: {result['success']}")
    print(f"Result reasoning: {result['reasoning']}")
    
    print("\n✅ All N8N API tests completed!")
    print("Configure webhook URL in config.py to enable live testing.")
    
    # Show sample payload structure
    print("\n" + "="*60)
    print("SAMPLE PAYLOAD STRUCTURE:")
    print("="*60)
    print(json.dumps(payload, indent=2))
