"""
Main Trading Bot

Orchestrates the entire trading system:
1. Loads active strategies using strategy_loader
2. Iterates through configured symbols
3. Executes strategies and collects signals
4. Formats results using prompt_formatter
5. Sends notifications via Telegram
6. (Future: AI agent confirmation)

Usage:
    python bot.py
"""

import time
import logging
import traceback
from datetime import datetime
from typing import Dict, Any, List
import sys

from strategy_loader import StrategyLoader
from prompt_formatter import format_prompt
from notify import NotificationManager
from utils import MarketDataProvider

sys.stdout.reconfigure(encoding='utf-8')

class SignalResult:
    """
    Standardized signal result structure.
    """

    def __init__(self, strategy_name: str, symbol: str, timeframe: str):
        self.strategy_name = strategy_name
        self.symbol = symbol
        self.timeframe = timeframe
        self.timestamp = datetime.now()

        # Signal data
        self.signal = None  # 'BUY', 'SELL', or None
        self.entry_price = 0.0
        self.stop_loss = 0.0
        self.take_profit = 0.0
        self.confidence = 0.0
        self.reason = ""

        # Metadata
        self.status = "no_signal"  # 'signal', 'no_signal', 'error'
        self.error_message = ""
        self.raw_result = {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for easy serialization."""
        return {
            'strategy_name': self.strategy_name,
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'timestamp': self.timestamp.isoformat(),
            'signal': self.signal,
            'entry_price': self.entry_price,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'confidence': self.confidence,
            'reason': self.reason,
            'status': self.status,
            'error_message': self.error_message
        }


class TradingBot:
    """
    Main trading bot class that orchestrates all components.
    """

    def __init__(self, bot_config: Dict[str, Any]):
        """
        Initialize the trading bot.

        Args:
            bot_config: Bot configuration dictionary
        """
        self.config = bot_config
        self.is_running = False

        # Setup logging first
        self._setup_logging()

        # Initialize components
        self.strategy_loader = StrategyLoader()
        self.prompt_formatter = format_prompt(bot_config)
        self.notification_manager = NotificationManager()
        self.market_data_provider = MarketDataProvider(bot_config)

        # Bot state
        self.cycle_count = 0
        self.total_signals_generated = 0
        self.last_run_time = None

        self.logger.info("Trading bot initialized successfully")

    def _setup_logging(self):
        """Setup logging configuration."""
        log_level = self.config.get('log_level', 'INFO')
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

        # Configure root logger with UTF-8 encoding
        logging.basicConfig(
            level=getattr(logging, log_level),
            format=log_format,
            handlers=[
                logging.FileHandler('trading_bot.log', encoding='utf-8', mode='a'),
                logging.StreamHandler(sys.stdout)
            ]
        )

        # Ensure stdout uses UTF-8 encoding
        if sys.stdout.encoding != 'utf-8':
            sys.stdout.reconfigure(encoding='utf-8')

        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Logging configured at {log_level} level")

    def start(self):
        """Start the trading bot main loop."""
        self.logger.info("=" * 60)
        self.logger.info("🚀 STARTING TRADING BOT")
        self.logger.info("=" * 60)

        # Validate configuration
        if not self._validate_config():
            self.logger.error("Configuration validation failed. Exiting.")
            return

        # Load and validate strategies
        if not self._validate_strategies():
            self.logger.error("Strategy validation failed. Exiting.")
            return

        self.is_running = True

        try:
            self._main_loop()
        except KeyboardInterrupt:
            self.logger.info("🛑 Bot stopped by user (Ctrl+C)")
        except Exception as e:
            self.logger.error(f"💥 Critical bot error: {e}")
            self.logger.error(traceback.format_exc())
        finally:
            self.stop()

    def stop(self):
        """Stop the trading bot."""
        self.logger.info("🔄 Stopping trading bot...")
        self.is_running = False

        # Send shutdown notification
        try:
            self.notification_manager.send_custom_message(
                f"🛑 Trading Bot Shutdown\n"
                f"Cycles completed: {self.cycle_count}\n"
                f"Total signals: {self.total_signals_generated}\n"
                f"Shutdown time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
        except Exception as e:
            self.logger.warning(f"Could not send shutdown notification: {e}")

        self.logger.info("✅ Trading bot stopped")

    def _validate_config(self) -> bool:
        """Validate bot configuration."""
        required_keys = ['symbols', 'timeframes']

        for key in required_keys:
            if key not in self.config:
                self.logger.error(f"Missing required config key: {key}")
                return False

        if not self.config['symbols']:
            self.logger.error("No symbols configured")
            return False

        if not self.config['timeframes']:
            self.logger.error("No timeframes configured")
            return False

        self.logger.info(f"✅ Configuration valid - Symbols: {self.config['symbols']}, Timeframes: {self.config['timeframes']}")
        return True

    def _validate_strategies(self) -> bool:
        """Validate that strategies can be loaded."""
        try:
            strategies = self.strategy_loader.load_enabled_strategies()

            if not strategies:
                self.logger.error("No strategies loaded. Check strategy_control.py")
                return False

            self.logger.info(f"✅ Loaded {len(strategies)} strategies:")
            for strategy in strategies:
                self.logger.info(f"   - {strategy.name} (valid: {strategy.is_valid})")

            return True

        except Exception as e:
            self.logger.error(f"Strategy validation failed: {e}")
            return False

    def _main_loop(self):
        """Main trading loop."""
        self.logger.info("🔄 Starting main trading loop...")

        while self.is_running:
            cycle_start_time = datetime.now()
            self.cycle_count += 1

            self.logger.info(f"\n{'='*50}")
            self.logger.info(f"📊 CYCLE {self.cycle_count} - {cycle_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            self.logger.info(f"{'='*50}")

            try:
                # Execute trading cycle
                cycle_results = self._execute_trading_cycle()

                # Process results
                self._process_cycle_results(cycle_results)

                # Update state
                self.last_run_time = cycle_start_time
                cycle_duration = (datetime.now() - cycle_start_time).total_seconds()

                self.logger.info(f"✅ Cycle {self.cycle_count} completed in {cycle_duration:.2f}s")

                # Sleep before next cycle
                sleep_time = self.config.get('loop_interval', 300)  # Default 5 minutes
                self.logger.info(f"😴 Sleeping for {sleep_time} seconds...")
                time.sleep(sleep_time)

            except Exception as e:
                self.logger.error(f"💥 Error in trading cycle {self.cycle_count}: {e}")
                self.logger.error(traceback.format_exc())

                # Sleep before retrying
                time.sleep(30)

    def _execute_trading_cycle(self) -> List[SignalResult]:
        """
        Execute one complete trading cycle.

        Returns:
            list: List of SignalResult objects
        """
        cycle_results = []

        # 1. Load active strategies
        self.logger.info("📥 Loading active strategies...")
        strategies = self.strategy_loader.load_enabled_strategies()

        if not strategies:
            self.logger.warning("⚠️ No strategies loaded")
            return cycle_results

        # 2. Get market data for all symbols and timeframes
        self.logger.info("📈 Fetching market data...")
        market_data = self._get_market_data()

        # 3. Execute strategies for each symbol and timeframe
        for symbol in self.config['symbols']:
            for timeframe in self.config['timeframes']:
                self.logger.info(f"🎯 Processing {symbol} {timeframe}...")

                # Get market data for this symbol/timeframe
                symbol_data = market_data.get(symbol, {}).get(timeframe, {})

                if symbol_data is None or (hasattr(symbol_data, "empty") and symbol_data.empty):
                    self.logger.warning(f"⚠️ No market data for {symbol} {timeframe}")
                    continue

                # Execute each strategy
                for strategy in strategies:
                    result = self._execute_strategy(strategy, symbol, timeframe, symbol_data)
                    cycle_results.append(result)

        return cycle_results

    def _get_market_data(self) -> Dict[str, Any]:
        """
        Get market data for all configured symbols and timeframes.

        Returns:
            dict: Market data organized by symbol and timeframe
        """
        try:
            # Use the market data provider to get data
            market_data = self.market_data_provider.get_current_data(
                symbols=self.config['symbols'],
                timeframes=self.config['timeframes']
            )

            self.logger.info(f"📊 Retrieved market data for {len(market_data)} symbols")
            return market_data

        except Exception as e:
            self.logger.error(f"💥 Error fetching market data: {e}")
            return {}

    def _execute_strategy(self, strategy, symbol: str, timeframe: str, market_data: Dict[str, Any]) -> SignalResult:
        """
        Execute a single strategy for a symbol/timeframe.

        Args:
            strategy: Strategy module to execute
            symbol: Trading symbol
            timeframe: Timeframe
            market_data: Market data for the symbol/timeframe

        Returns:
            SignalResult: Standardized result object
        """
        result = SignalResult(strategy.name, symbol, timeframe)

        try:
            self.logger.debug(f"🔍 Executing {strategy.name} for {symbol} {timeframe}")

            # Execute strategy
            strategy_result = strategy.run(
                symbol=symbol,
                timeframe=timeframe,
                market_data=market_data
            )

            # Store raw result
            result.raw_result = strategy_result

            # Parse strategy result
            self._parse_strategy_result(result, strategy_result)

            # Log result
            if result.status == 'signal':
                self.logger.info(f"🎯 {strategy.name}: {result.signal} signal for {symbol} (confidence: {result.confidence:.1%})")
                self.total_signals_generated += 1
            else:
                self.logger.debug(f"📊 {strategy.name}: No signal for {symbol}")

        except Exception as e:
            result.status = 'error'
            result.error_message = str(e)
            self.logger.error(f"💥 Error executing {strategy.name} for {symbol}: {e}")

        return result

    def _parse_strategy_result(self, result: SignalResult, strategy_result: Dict[str, Any]):
        """
        Parse strategy result into standardized format.

        Args:
            result: SignalResult object to populate
            strategy_result: Raw strategy result dictionary
        """
        # Check for errors
        if strategy_result.get('status') == 'error':
            result.status = 'error'
            result.error_message = strategy_result.get('error', 'Unknown error')
            return

        # Check for signals
        signals = strategy_result.get('signals', [])

        if not signals:
            result.status = 'no_signal'
            return

        # Use the first signal (strategies can return multiple signals)
        signal_data = signals[0]

        result.status = 'signal'
        result.signal = signal_data.get('action', '').upper()  # 'BUY' or 'SELL'
        result.entry_price = signal_data.get('price', 0.0)
        result.stop_loss = signal_data.get('stop_loss', 0.0)
        result.take_profit = signal_data.get('take_profit', 0.0)
        result.confidence = signal_data.get('confidence', 0.0)
        result.reason = signal_data.get('reason', strategy_result.get('reason', 'No reason provided'))

    def _process_cycle_results(self, results: List[SignalResult]):
        """
        Process the results from a trading cycle.

        Args:
            results: List of SignalResult objects
        """
        # Filter for actual signals
        signals = [r for r in results if r.status == 'signal']
        errors = [r for r in results if r.status == 'error']

        self.logger.info(f"📊 Cycle Results: {len(signals)} signals, {len(errors)} errors, {len(results)} total")

        # Send notifications for signals
        for signal_result in signals:
            try:
                self._send_signal_notification(signal_result)
            except Exception as e:
                self.logger.error(f"💥 Error sending notification for {signal_result.strategy_name}: {e}")

        # Log errors
        for error_result in errors:
            self.logger.error(f"💥 Strategy error - {error_result.strategy_name}: {error_result.error_message}")

        # Send cycle summary if configured
        if self.config.get('send_cycle_summary', False) and (signals or errors):
            self._send_cycle_summary(results)

    def _send_signal_notification(self, signal_result: SignalResult):
        """
        Send notification for a trading signal.

        Args:
            signal_result: SignalResult object
        """
        # Format the signal using prompt formatter
        formatted_message = self._format_signal_message(signal_result)

        # Send to Telegram
        self.notification_manager.send_custom_message(formatted_message)

        self.logger.info(f"📤 Sent notification for {signal_result.strategy_name} signal")

    def _format_signal_message(self, signal_result: SignalResult) -> str:
        """
        Format a signal result into a readable message.

        Args:
            signal_result: SignalResult object

        Returns:
            str: Formatted message
        """
        # Calculate risk/reward ratio
        risk = abs(signal_result.entry_price - signal_result.stop_loss) if signal_result.stop_loss else 0
        reward = abs(signal_result.take_profit - signal_result.entry_price) if signal_result.take_profit else 0
        rr_ratio = reward / risk if risk > 0 else 0

        # Choose emoji based on signal type
        signal_emoji = "🟢" if signal_result.signal == "BUY" else "🔴"
        confidence_emoji = "🔥" if signal_result.confidence > 0.8 else "⚡" if signal_result.confidence > 0.6 else "💡"

        message = f"""
{signal_emoji} **TRADING SIGNAL** {signal_emoji}

{confidence_emoji} **Strategy:** {signal_result.strategy_name}
📊 **Symbol:** {signal_result.symbol}
⏰ **Timeframe:** {signal_result.timeframe}
🎯 **Action:** {signal_result.signal}

💰 **Entry Price:** ${signal_result.entry_price:.4f}
🛑 **Stop Loss:** ${signal_result.stop_loss:.4f}
🎯 **Take Profit:** ${signal_result.take_profit:.4f}
📈 **Risk/Reward:** 1:{rr_ratio:.2f}

🎲 **Confidence:** {signal_result.confidence:.1%}
💭 **Reason:** {signal_result.reason}

⏱ **Time:** {signal_result.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
🔄 **Cycle:** {self.cycle_count}
        """.strip()

        return message

    def _send_cycle_summary(self, results: List[SignalResult]):
        """
        Send a summary of the trading cycle.

        Args:
            results: List of SignalResult objects
        """
        signals = [r for r in results if r.status == 'signal']
        errors = [r for r in results if r.status == 'error']

        # Group signals by strategy
        strategy_signals = {}
        for signal in signals:
            if signal.strategy_name not in strategy_signals:
                strategy_signals[signal.strategy_name] = []
            strategy_signals[signal.strategy_name].append(signal)

        message = f"""
📊 **CYCLE {self.cycle_count} SUMMARY**

🎯 **Signals Generated:** {len(signals)}
💥 **Errors:** {len(errors)}
⏱ **Time:** {datetime.now().strftime('%H:%M:%S')}

**Signals by Strategy:**
        """

        for strategy_name, strategy_signals_list in strategy_signals.items():
            message += f"\n• {strategy_name}: {len(strategy_signals_list)} signals"

        if errors:
            message += f"\n\n**Errors:**"
            for error in errors[:3]:  # Limit to first 3 errors
                message += f"\n• {error.strategy_name}: {error.error_message[:50]}..."

        self.notification_manager.send_custom_message(message)


def main():
    """Main entry point."""
    try:
        # Import config here to avoid circular imports
        import config

        # Validate configuration first
        if not config.validate_config():
            print("❌ Configuration validation failed. Please check config.py")
            return

        # Print configuration summary
        config.print_config_summary()

        # Load configuration
        bot_config = config.get_config()

        # Create and start bot
        bot = TradingBot(bot_config)
        bot.start()

    except Exception as e:
        logging.error(f"Failed to start bot: {e}")
        logging.error(traceback.format_exc())


if __name__ == "__main__":
    main()
