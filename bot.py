"""
Main trading bot entry point.
Runs the main loop and orchestrates strategy execution.
"""

import time
import logging
from datetime import datetime
from typing import Dict, Any

from strategy_loader import StrategyLoader
from trade import TradeManager
from notify import NotificationManager
from utils import MarketDataProvider


class TradingBot:
    """
    Main trading bot class that orchestrates all components.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the trading bot with configuration.
        
        Args:
            config: Bot configuration dictionary
        """
        self.config = config
        self.strategy_loader = StrategyLoader()
        self.trade_manager = TradeManager(config)
        self.notification_manager = NotificationManager(config)
        self.market_data = MarketDataProvider(config)
        self.is_running = False
        
        # Setup logging
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('trading_bot.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def start(self):
        """Start the trading bot main loop."""
        self.logger.info("Starting trading bot...")
        self.is_running = True
        
        try:
            self._main_loop()
        except KeyboardInterrupt:
            self.logger.info("Bot stopped by user")
        except Exception as e:
            self.logger.error(f"Bot error: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """Stop the trading bot."""
        self.logger.info("Stopping trading bot...")
        self.is_running = False
    
    def _main_loop(self):
        """Main trading loop."""
        while self.is_running:
            try:
                # Load enabled strategies
                strategies = self.strategy_loader.load_enabled_strategies()
                
                # Get market data
                market_data = self.market_data.get_current_data()
                
                # Execute strategies
                for strategy in strategies:
                    signals = strategy.analyze(market_data)
                    if signals:
                        self._process_signals(signals, strategy.name)
                
                # Sleep before next iteration
                time.sleep(self.config.get('loop_interval', 60))
                
            except Exception as e:
                self.logger.error(f"Error in main loop: {e}")
                time.sleep(10)
    
    def _process_signals(self, signals: list, strategy_name: str):
        """
        Process trading signals from strategies.
        
        Args:
            signals: List of trading signals
            strategy_name: Name of the strategy that generated signals
        """
        for signal in signals:
            # Execute trade through trade manager
            trade_result = self.trade_manager.execute_trade(signal)
            
            # Send notification
            if trade_result:
                self.notification_manager.send_trade_notification(
                    trade_result, strategy_name
                )


def main():
    """Main entry point."""
    # Load configuration
    config = {
        'exchange': 'binance',
        'api_key': 'your_api_key',
        'api_secret': 'your_api_secret',
        'loop_interval': 60,
        'telegram_token': 'your_telegram_token',
        'telegram_chat_id': 'your_chat_id'
    }
    
    # Create and start bot
    bot = TradingBot(config)
    bot.start()


if __name__ == "__main__":
    main()
