"""
OpenAI/ChatGPT client for AI-powered trading analysis.
"""

import openai
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from prompt_formatter import PromptFormatter
from strategies.base_strategy import TradingSignal
from trade import TradeRecord


class OpenAIClient:
    """
    Client for interacting with OpenAI's GPT models for trading analysis.
    """
    
    def __init__(self, api_key: str, model: str = "gpt-4", config: Dict[str, Any] = None):
        """
        Initialize OpenAI client.
        
        Args:
            api_key: OpenAI API key
            model: Model to use (gpt-3.5-turbo, gpt-4, etc.)
            config: Additional configuration
        """
        self.api_key = api_key
        self.model = model
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self.prompt_formatter = PromptFormatter(config)
        
        # Initialize OpenAI client
        openai.api_key = api_key
        
        # Model parameters
        self.max_tokens = self.config.get('max_tokens', 2000)
        self.temperature = self.config.get('temperature', 0.7)
        self.top_p = self.config.get('top_p', 1.0)
    
    def analyze_trading_signal(self, signal: TradingSignal, 
                             market_data: Dict[str, Any],
                             trade_history: List[TradeRecord] = None) -> Dict[str, Any]:
        """
        Get AI analysis of a trading signal.
        
        Args:
            signal: Trading signal to analyze
            market_data: Current market data
            trade_history: Recent trade history
            
        Returns:
            dict: AI analysis results
        """
        try:
            # Format prompt
            prompt = self.prompt_formatter.format_signal_analysis_prompt(
                signal, market_data, trade_history
            )
            
            # Get AI response
            response = self._make_completion_request(prompt)
            
            # Parse response
            analysis = self._parse_signal_analysis(response)
            
            self.logger.info(f"AI analysis completed for {signal.symbol} signal")
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error in AI signal analysis: {e}")
            return {'error': str(e), 'recommendation': 'REJECT'}
    
    def review_trade_performance(self, trade_record: TradeRecord,
                               market_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Get AI review of trade performance.
        
        Args:
            trade_record: Completed trade record
            market_data: Market data during trade period
            
        Returns:
            dict: AI review results
        """
        try:
            # Format prompt
            prompt = self.prompt_formatter.format_trade_review_prompt(
                trade_record, market_data
            )
            
            # Get AI response
            response = self._make_completion_request(prompt)
            
            # Parse response
            review = self._parse_trade_review(response)
            
            self.logger.info(f"AI trade review completed for {trade_record.trade_id}")
            return review
            
        except Exception as e:
            self.logger.error(f"Error in AI trade review: {e}")
            return {'error': str(e)}
    
    def analyze_portfolio_performance(self, trades: List[TradeRecord],
                                    account_balance: float,
                                    time_period: str = "last 30 days") -> Dict[str, Any]:
        """
        Get AI analysis of portfolio performance.
        
        Args:
            trades: List of trades to analyze
            account_balance: Current account balance
            time_period: Time period for analysis
            
        Returns:
            dict: AI portfolio analysis
        """
        try:
            # Format prompt
            prompt = self.prompt_formatter.format_portfolio_analysis_prompt(
                trades, account_balance, time_period
            )
            
            # Get AI response
            response = self._make_completion_request(prompt)
            
            # Parse response
            analysis = self._parse_portfolio_analysis(response)
            
            self.logger.info("AI portfolio analysis completed")
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error in AI portfolio analysis: {e}")
            return {'error': str(e)}
    
    def analyze_market_conditions(self, market_data: Dict[str, Any],
                                symbols: List[str] = None) -> Dict[str, Any]:
        """
        Get AI analysis of current market conditions.
        
        Args:
            market_data: Current market data
            symbols: List of symbols to analyze
            
        Returns:
            dict: AI market analysis
        """
        try:
            # Format prompt
            prompt = self.prompt_formatter.format_market_analysis_prompt(
                market_data, symbols
            )
            
            # Get AI response
            response = self._make_completion_request(prompt)
            
            # Parse response
            analysis = self._parse_market_analysis(response)
            
            self.logger.info("AI market analysis completed")
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error in AI market analysis: {e}")
            return {'error': str(e)}
    
    def get_strategy_recommendations(self, performance_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get AI recommendations for strategy improvements.
        
        Args:
            performance_data: Strategy performance data
            
        Returns:
            dict: AI strategy recommendations
        """
        try:
            # Create custom prompt for strategy recommendations
            prompt = self._create_strategy_recommendation_prompt(performance_data)
            
            # Get AI response
            response = self._make_completion_request(prompt)
            
            # Parse response
            recommendations = self._parse_strategy_recommendations(response)
            
            self.logger.info("AI strategy recommendations completed")
            return recommendations
            
        except Exception as e:
            self.logger.error(f"Error in AI strategy recommendations: {e}")
            return {'error': str(e)}
    
    def _make_completion_request(self, prompt: str) -> str:
        """
        Make completion request to OpenAI API.
        
        Args:
            prompt: Formatted prompt
            
        Returns:
            str: AI response
        """
        try:
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert trading analyst with deep knowledge of financial markets, technical analysis, and risk management. Provide detailed, actionable insights based on the data provided."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                top_p=self.top_p
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            self.logger.error(f"Error making OpenAI request: {e}")
            raise
    
    def _parse_signal_analysis(self, response: str) -> Dict[str, Any]:
        """
        Parse AI response for signal analysis.
        
        Args:
            response: Raw AI response
            
        Returns:
            dict: Parsed analysis
        """
        # TODO: Implement sophisticated response parsing
        # This could use regex, NLP, or structured prompts to extract:
        # - Recommendation (EXECUTE, MODIFY, REJECT)
        # - Confidence score
        # - Risk assessment
        # - Suggested modifications
        # - Reasoning
        
        # Simple parsing for now
        recommendation = 'EXECUTE'
        if 'reject' in response.lower() or 'avoid' in response.lower():
            recommendation = 'REJECT'
        elif 'modify' in response.lower() or 'adjust' in response.lower():
            recommendation = 'MODIFY'
        
        return {
            'recommendation': recommendation,
            'confidence': 0.7,  # Default confidence
            'analysis': response,
            'timestamp': datetime.now().isoformat()
        }
    
    def _parse_trade_review(self, response: str) -> Dict[str, Any]:
        """Parse AI response for trade review."""
        return {
            'review': response,
            'lessons_learned': [],  # TODO: Extract key lessons
            'improvements': [],  # TODO: Extract improvement suggestions
            'rating': 'Good',  # TODO: Extract performance rating
            'timestamp': datetime.now().isoformat()
        }
    
    def _parse_portfolio_analysis(self, response: str) -> Dict[str, Any]:
        """Parse AI response for portfolio analysis."""
        return {
            'analysis': response,
            'strengths': [],  # TODO: Extract strengths
            'weaknesses': [],  # TODO: Extract weaknesses
            'recommendations': [],  # TODO: Extract recommendations
            'action_plan': [],  # TODO: Extract action items
            'timestamp': datetime.now().isoformat()
        }
    
    def _parse_market_analysis(self, response: str) -> Dict[str, Any]:
        """Parse AI response for market analysis."""
        return {
            'analysis': response,
            'trend': 'Neutral',  # TODO: Extract trend direction
            'volatility': 'Medium',  # TODO: Extract volatility assessment
            'opportunities': [],  # TODO: Extract opportunities
            'risks': [],  # TODO: Extract risks
            'timestamp': datetime.now().isoformat()
        }
    
    def _parse_strategy_recommendations(self, response: str) -> Dict[str, Any]:
        """Parse AI response for strategy recommendations."""
        return {
            'recommendations': response,
            'priority_actions': [],  # TODO: Extract priority actions
            'parameter_adjustments': {},  # TODO: Extract parameter suggestions
            'new_strategies': [],  # TODO: Extract new strategy suggestions
            'timestamp': datetime.now().isoformat()
        }
    
    def _create_strategy_recommendation_prompt(self, performance_data: Dict[str, Any]) -> str:
        """Create prompt for strategy recommendations."""
        prompt = f"""
# Strategy Performance Analysis and Recommendations

## Performance Data
{json.dumps(performance_data, indent=2)}

## Request
Based on the performance data above, please provide:

1. **Performance Assessment**: Overall evaluation of strategy performance
2. **Key Issues**: Identify main problems or areas of concern
3. **Parameter Adjustments**: Specific parameter changes to improve performance
4. **Risk Management**: Recommendations for better risk management
5. **New Strategies**: Suggestions for additional strategies to consider
6. **Priority Actions**: Top 3 actions to implement immediately

Please provide specific, actionable recommendations with clear reasoning.
        """
        
        return prompt.strip()


class AIAnalysisManager:
    """
    Manages AI analysis workflows and caching.
    """
    
    def __init__(self, openai_client: OpenAIClient, config: Dict[str, Any] = None):
        """
        Initialize AI analysis manager.
        
        Args:
            openai_client: OpenAI client instance
            config: Configuration dictionary
        """
        self.openai_client = openai_client
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self.analysis_cache = {}
        self.cache_duration = self.config.get('cache_duration', 3600)  # 1 hour
    
    def get_signal_analysis(self, signal: TradingSignal, 
                          market_data: Dict[str, Any],
                          trade_history: List[TradeRecord] = None,
                          use_cache: bool = True) -> Dict[str, Any]:
        """
        Get AI analysis for trading signal with caching.
        
        Args:
            signal: Trading signal
            market_data: Market data
            trade_history: Trade history
            use_cache: Whether to use cached results
            
        Returns:
            dict: Analysis results
        """
        # Create cache key
        cache_key = f"signal_{signal.symbol}_{signal.action}_{signal.timestamp.isoformat()}"
        
        # Check cache
        if use_cache and self._is_cache_valid(cache_key):
            self.logger.info(f"Using cached analysis for {cache_key}")
            return self.analysis_cache[cache_key]['data']
        
        # Get fresh analysis
        analysis = self.openai_client.analyze_trading_signal(
            signal, market_data, trade_history
        )
        
        # Cache result
        if use_cache:
            self.analysis_cache[cache_key] = {
                'data': analysis,
                'timestamp': datetime.now()
            }
        
        return analysis
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid."""
        if cache_key not in self.analysis_cache:
            return False
        
        cache_time = self.analysis_cache[cache_key]['timestamp']
        return (datetime.now() - cache_time).total_seconds() < self.cache_duration
    
    def clear_cache(self):
        """Clear analysis cache."""
        self.analysis_cache.clear()
        self.logger.info("AI analysis cache cleared")
