"""
Real-Time Market Data Provider using CCXT

This module provides real-time market data from cryptocurrency exchanges
using the CCXT library. It fetches live OHLCV data and validates prices
to ensure accuracy.

Features:
- Real-time OHLCV data from Binance/BingX
- Symbol validation using exchange.load_markets()
- Price validation (5% threshold)
- Proper symbol format conversion (DOTUSDT -> DOT/USDT)
- Caching to avoid rate limits
- Error handling and fallback mechanisms
"""

import ccxt
import pandas as pd
import numpy as np
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# Import technical indicators directly from ta library to avoid circular imports
import ta


class RealTimeMarketDataProvider:
    """
    Real-time market data provider using CCXT for live trading.
    
    Fetches real OHLCV data from exchanges and validates prices to ensure
    signals are generated with accurate, up-to-date market data.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the real-time market data provider.
        
        Args:
            config: Configuration dictionary containing exchange settings
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.cache = {}
        self.cache_duration = config.get('market_data', {}).get('cache_duration', 60)  # 60 seconds
        self.max_candles = config.get('market_data', {}).get('max_candles', 200)
        
        # Initialize exchange
        self.exchange = None
        self.markets = {}
        self.symbol_map = {}  # Maps DOTUSDT -> DOT/USDT
        
        self._initialize_exchange()
        
    def _initialize_exchange(self):
        """Initialize the exchange connection and load markets."""
        try:
            exchange_name = self.config.get('exchange', 'binance').lower()
            
            # Initialize exchange based on config
            if exchange_name == 'binance':
                self.exchange = ccxt.binance({
                    'apiKey': self.config.get('api_key', ''),
                    'secret': self.config.get('api_secret', ''),
                    'sandbox': self.config.get('testnet', True),  # Use testnet by default
                    'enableRateLimit': True,
                    'timeout': 30000,
                })
            elif exchange_name == 'bingx':
                self.exchange = ccxt.bingx({
                    'apiKey': self.config.get('api_key', ''),
                    'secret': self.config.get('api_secret', ''),
                    'sandbox': self.config.get('testnet', True),
                    'enableRateLimit': True,
                    'timeout': 30000,
                })
            else:
                # Default to Binance
                self.exchange = ccxt.binance({
                    'enableRateLimit': True,
                    'timeout': 30000,
                })
            
            # Load markets and create symbol mapping
            self.markets = self.exchange.load_markets()
            self._create_symbol_mapping()
            
            self.logger.info(f"✅ Connected to {exchange_name.upper()} exchange")
            self.logger.info(f"📊 Loaded {len(self.markets)} trading pairs")
            
        except Exception as e:
            self.logger.error(f"💥 Failed to initialize exchange: {e}")
            # Fallback to demo mode
            self.exchange = None
            self.markets = {}
            
    def _create_symbol_mapping(self):
        """Create mapping from DOTUSDT format to DOT/USDT format."""
        self.symbol_map = {}
        
        for symbol in self.markets.keys():
            # Convert DOT/USDT to DOTUSDT for reverse lookup
            if '/' in symbol:
                base, quote = symbol.split('/')
                compact_symbol = f"{base}{quote}"
                self.symbol_map[compact_symbol] = symbol
                
        self.logger.debug(f"Created symbol mapping for {len(self.symbol_map)} pairs")
        
    def _convert_symbol_format(self, symbol: str) -> str:
        """
        Convert symbol from DOTUSDT format to DOT/USDT format.
        
        Args:
            symbol: Symbol in DOTUSDT format
            
        Returns:
            str: Symbol in DOT/USDT format
        """
        if symbol in self.symbol_map:
            return self.symbol_map[symbol]
        
        # If not found in mapping, try common patterns
        if symbol.endswith('USDT'):
            base = symbol[:-4]  # Remove 'USDT'
            return f"{base}/USDT"
        elif symbol.endswith('BTC'):
            base = symbol[:-3]  # Remove 'BTC'
            return f"{base}/BTC"
        elif symbol.endswith('ETH'):
            base = symbol[:-3]  # Remove 'ETH'
            return f"{base}/ETH"
        
        # Return as-is if no conversion possible
        return symbol
        
    def _validate_symbol(self, symbol: str) -> bool:
        """
        Validate if symbol exists on the exchange.
        
        Args:
            symbol: Symbol to validate (in DOT/USDT format)
            
        Returns:
            bool: True if symbol is valid
        """
        return symbol in self.markets
        
    def _convert_timeframe(self, timeframe: str) -> str:
        """
        Convert timeframe to exchange format.
        
        Args:
            timeframe: Timeframe (e.g., '1h', '4h', '1d')
            
        Returns:
            str: Exchange-compatible timeframe
        """
        # CCXT standard timeframes
        timeframe_map = {
            '1m': '1m',
            '5m': '5m',
            '15m': '15m',
            '30m': '30m',
            '1h': '1h',
            '2h': '2h',
            '4h': '4h',
            '6h': '6h',
            '8h': '8h',
            '12h': '12h',
            '1d': '1d',
            '3d': '3d',
            '1w': '1w',
            '1M': '1M'
        }
        
        return timeframe_map.get(timeframe, timeframe)
        
    def _fetch_ohlcv_data(self, symbol: str, timeframe: str) -> pd.DataFrame:
        """
        Fetch OHLCV data from exchange.
        
        Args:
            symbol: Trading symbol in DOT/USDT format
            timeframe: Timeframe for data
            
        Returns:
            pd.DataFrame: OHLCV data with columns [timestamp, open, high, low, close, volume]
        """
        try:
            if not self.exchange:
                raise Exception("Exchange not initialized")
                
            # Convert timeframe
            tf = self._convert_timeframe(timeframe)
            
            # Fetch OHLCV data
            ohlcv = self.exchange.fetch_ohlcv(
                symbol=symbol,
                timeframe=tf,
                limit=self.max_candles
            )
            
            if not ohlcv:
                raise Exception(f"No data returned for {symbol} {timeframe}")
            
            # Convert to DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # Convert timestamp to datetime
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            # Ensure numeric types
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # Remove any rows with NaN values
            df = df.dropna()
            
            if len(df) == 0:
                raise Exception(f"No valid data after cleaning for {symbol} {timeframe}")
            
            self.logger.debug(f"📈 Fetched {len(df)} candles for {symbol} {timeframe}")
            return df
            
        except Exception as e:
            self.logger.error(f"💥 Error fetching OHLCV data for {symbol} {timeframe}: {e}")
            raise
            
    def _get_current_price(self, symbol: str) -> float:
        """
        Get current market price for symbol.
        
        Args:
            symbol: Trading symbol in DOT/USDT format
            
        Returns:
            float: Current price
        """
        try:
            if not self.exchange:
                raise Exception("Exchange not initialized")
                
            ticker = self.exchange.fetch_ticker(symbol)
            current_price = float(ticker['last'])
            
            self.logger.debug(f"💰 Current price for {symbol}: ${current_price:.6f}")
            return current_price
            
        except Exception as e:
            self.logger.error(f"💥 Error fetching current price for {symbol}: {e}")
            raise
            
    def _validate_price_accuracy(self, symbol: str, generated_price: float, 
                                current_price: float, threshold: float = 0.05) -> Tuple[bool, float]:
        """
        Validate if generated price is within acceptable range of current market price.
        
        Args:
            symbol: Trading symbol
            generated_price: Price generated by strategy
            current_price: Current market price
            threshold: Maximum allowed difference (default: 5%)
            
        Returns:
            tuple: (is_valid, price_difference_percent)
        """
        if current_price <= 0 or generated_price <= 0:
            return False, float('inf')
            
        price_diff = abs(generated_price - current_price) / current_price
        is_valid = price_diff <= threshold
        
        self.logger.debug(f"🔍 Price validation for {symbol}: "
                         f"Generated: ${generated_price:.6f}, "
                         f"Current: ${current_price:.6f}, "
                         f"Diff: {price_diff:.2%}, "
                         f"Valid: {is_valid}")
        
        return is_valid, price_diff

    def _add_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add technical indicators to OHLCV data.

        Args:
            df: DataFrame with OHLCV data

        Returns:
            pd.DataFrame: DataFrame with added indicators
        """
        try:
            df_with_indicators = df.copy()

            # Add RSI
            df_with_indicators['rsi'] = ta.momentum.RSIIndicator(
                close=df['close'], window=14
            ).rsi().fillna(50.0)

            # Add ATR
            df_with_indicators['atr'] = ta.volatility.AverageTrueRange(
                high=df['high'], low=df['low'], close=df['close'], window=14
            ).average_true_range().fillna(0.01)

            # Add moving averages
            df_with_indicators['sma_20'] = ta.trend.SMAIndicator(
                close=df['close'], window=20
            ).sma_indicator().bfill()
            df_with_indicators['ema_20'] = ta.trend.EMAIndicator(
                close=df['close'], window=20
            ).ema_indicator().bfill()
            df_with_indicators['sma_50'] = ta.trend.SMAIndicator(
                close=df['close'], window=50
            ).sma_indicator().bfill()
            df_with_indicators['ema_50'] = ta.trend.EMAIndicator(
                close=df['close'], window=50
            ).ema_indicator().bfill()

            # Add MACD
            macd_indicator = ta.trend.MACD(
                close=df['close'], window_fast=12, window_slow=26, window_sign=9
            )
            df_with_indicators['macd'] = macd_indicator.macd().fillna(0)
            df_with_indicators['macd_signal'] = macd_indicator.macd_signal().fillna(0)
            df_with_indicators['macd_histogram'] = macd_indicator.macd_diff().fillna(0)

            # Add Bollinger Bands
            bb_indicator = ta.volatility.BollingerBands(
                close=df['close'], window=20, window_dev=2.0
            )
            df_with_indicators['bb_upper'] = bb_indicator.bollinger_hband().bfill()
            df_with_indicators['bb_middle'] = bb_indicator.bollinger_mavg().bfill()
            df_with_indicators['bb_lower'] = bb_indicator.bollinger_lband().bfill()

            self.logger.debug(f"📊 Added technical indicators to data")
            return df_with_indicators

        except Exception as e:
            self.logger.error(f"💥 Error adding indicators: {e}")
            return df

    def get_current_data(self, symbols: List[str] = None, timeframes: List[str] = None) -> Dict[str, Any]:
        """
        Get current market data for specified symbols and timeframes.

        This is the main method that replaces the simulated data provider.
        It fetches real-time data and validates prices.

        Args:
            symbols: List of symbols in DOTUSDT format
            timeframes: List of timeframes

        Returns:
            dict: Market data organized by symbol and timeframe
        """
        if symbols is None:
            symbols = ['BTCUSDT', 'ETHUSDT']

        if timeframes is None:
            timeframes = ['1h', '4h']

        market_data = {}

        for symbol in symbols:
            market_data[symbol] = {}

            # Convert symbol format
            exchange_symbol = self._convert_symbol_format(symbol)

            # Validate symbol
            if not self._validate_symbol(exchange_symbol):
                self.logger.warning(f"⚠️ Invalid symbol: {symbol} ({exchange_symbol})")
                continue

            for timeframe in timeframes:
                try:
                    # Check cache first
                    cache_key = f"{symbol}_{timeframe}"
                    current_time = time.time()

                    if (cache_key in self.cache and
                        current_time - self.cache[cache_key]['timestamp'] < self.cache_duration):
                        self.logger.debug(f"📋 Using cached data for {symbol} {timeframe}")
                        market_data[symbol][timeframe] = self.cache[cache_key]['data']
                        continue

                    # Fetch fresh data
                    self.logger.info(f"📈 Fetching real-time data for {symbol} {timeframe}...")

                    # Get OHLCV data
                    df = self._fetch_ohlcv_data(exchange_symbol, timeframe)

                    # Add technical indicators
                    df_with_indicators = self._add_indicators(df)

                    # Get current price for validation
                    current_price = self._get_current_price(exchange_symbol)
                    latest_close = df_with_indicators['close'].iloc[-1]

                    # Validate price accuracy
                    is_valid, price_diff = self._validate_price_accuracy(
                        symbol, latest_close, current_price
                    )

                    if not is_valid:
                        self.logger.warning(f"⚠️ Price mismatch for {symbol}: "
                                          f"Latest close: ${latest_close:.6f}, "
                                          f"Current: ${current_price:.6f}, "
                                          f"Diff: {price_diff:.2%}")

                    # Add metadata
                    df_with_indicators.attrs = {
                        'symbol': symbol,
                        'exchange_symbol': exchange_symbol,
                        'timeframe': timeframe,
                        'current_price': current_price,
                        'latest_close': latest_close,
                        'price_valid': is_valid,
                        'price_diff': price_diff,
                        'fetch_time': datetime.now().isoformat()
                    }

                    # Cache the data
                    self.cache[cache_key] = {
                        'data': df_with_indicators,
                        'timestamp': current_time
                    }

                    market_data[symbol][timeframe] = df_with_indicators

                    self.logger.info(f"✅ Successfully fetched {len(df_with_indicators)} candles for {symbol} {timeframe}")

                except Exception as e:
                    self.logger.error(f"💥 Error getting data for {symbol} {timeframe}: {e}")
                    market_data[symbol][timeframe] = {}

        return market_data

    def validate_signal_price(self, symbol: str, signal_price: float, threshold: float = 0.05) -> Dict[str, Any]:
        """
        Validate a trading signal price against current market price.

        Args:
            symbol: Trading symbol in DOTUSDT format
            signal_price: Price from trading signal
            threshold: Maximum allowed difference (default: 5%)

        Returns:
            dict: Validation result with current price and recommendation
        """
        try:
            # Convert symbol format
            exchange_symbol = self._convert_symbol_format(symbol)

            # Get current price
            current_price = self._get_current_price(exchange_symbol)

            # Validate price
            is_valid, price_diff = self._validate_price_accuracy(
                symbol, signal_price, current_price, threshold
            )

            result = {
                'symbol': symbol,
                'signal_price': signal_price,
                'current_price': current_price,
                'price_difference_percent': price_diff * 100,
                'is_valid': is_valid,
                'threshold_percent': threshold * 100,
                'recommendation': 'ACCEPT' if is_valid else 'REJECT',
                'timestamp': datetime.now().isoformat()
            }

            if not is_valid:
                result['reason'] = f"Price difference ({price_diff:.2%}) exceeds threshold ({threshold:.2%})"

            return result

        except Exception as e:
            self.logger.error(f"💥 Error validating signal price for {symbol}: {e}")
            return {
                'symbol': symbol,
                'signal_price': signal_price,
                'current_price': None,
                'is_valid': False,
                'recommendation': 'REJECT',
                'reason': f"Error fetching current price: {e}",
                'timestamp': datetime.now().isoformat()
            }
