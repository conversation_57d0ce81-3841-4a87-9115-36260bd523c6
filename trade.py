"""
Trade management module for position sizing, SL/TP calculation, and trade logging.
"""

import csv
import os
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
import logging

from strategies.base_strategy import TradingSignal


@dataclass
class TradeRecord:
    """
    Represents a completed trade record.
    """
    trade_id: str
    symbol: str
    strategy: str
    action: str  # 'BUY', 'SELL'
    entry_price: float
    exit_price: Optional[float] = None
    quantity: float = 0.0
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    entry_time: datetime = None
    exit_time: Optional[datetime] = None
    pnl: float = 0.0
    pnl_percentage: float = 0.0
    fees: float = 0.0
    status: str = 'OPEN'  # 'OPEN', 'CLOSED', 'CANCELLED'
    timeframe: str = '1h'
    confidence: float = 0.0
    notes: str = ''
    
    def __post_init__(self):
        if self.entry_time is None:
            self.entry_time = datetime.now()


class PositionSizer:
    """
    Handles position sizing calculations based on risk management rules.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize position sizer.
        
        Args:
            config: Configuration dictionary with risk parameters
        """
        self.max_risk_per_trade = config.get('max_risk_per_trade', 0.02)  # 2%
        self.max_position_size = config.get('max_position_size', 0.1)  # 10%
        self.min_position_size = config.get('min_position_size', 0.001)  # 0.1%
        self.use_kelly_criterion = config.get('use_kelly_criterion', False)
        self.volatility_adjustment = config.get('volatility_adjustment', True)
    
    def calculate_position_size(self, signal: TradingSignal, account_balance: float, 
                              market_data: Dict[str, Any]) -> float:
        """
        Calculate optimal position size for a trading signal.
        
        Args:
            signal: Trading signal
            account_balance: Current account balance
            market_data: Current market data
            
        Returns:
            float: Position size as percentage of account balance
        """
        # TODO: Implement position sizing logic
        # 1. Calculate risk amount based on stop loss
        # 2. Adjust for volatility (ATR)
        # 3. Apply Kelly criterion if enabled
        # 4. Ensure position size is within limits
        
        base_risk = self.max_risk_per_trade
        
        # Adjust for signal confidence
        confidence_multiplier = signal.confidence
        adjusted_risk = base_risk * confidence_multiplier
        
        # Volatility adjustment
        if self.volatility_adjustment:
            volatility_factor = self._get_volatility_factor(market_data)
            adjusted_risk *= volatility_factor
        
        # Ensure within limits
        position_size = max(self.min_position_size, 
                          min(adjusted_risk, self.max_position_size))
        
        return position_size
    
    def _get_volatility_factor(self, market_data: Dict[str, Any]) -> float:
        """
        Calculate volatility adjustment factor.
        
        Args:
            market_data: Market data containing volatility indicators
            
        Returns:
            float: Volatility adjustment factor
        """
        # TODO: Implement volatility calculation
        # Use ATR or other volatility measures
        # Higher volatility = lower position size
        
        return 1.0  # Placeholder


class RiskManager:
    """
    Handles stop loss and take profit calculations.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize risk manager.
        
        Args:
            config: Configuration dictionary
        """
        self.default_stop_loss_pct = config.get('default_stop_loss_pct', 0.02)  # 2%
        self.default_take_profit_pct = config.get('default_take_profit_pct', 0.04)  # 4%
        self.use_atr_stops = config.get('use_atr_stops', True)
        self.atr_multiplier = config.get('atr_multiplier', 2.0)
        self.risk_reward_ratio = config.get('risk_reward_ratio', 2.0)
    
    def calculate_stop_loss(self, signal: TradingSignal, market_data: Dict[str, Any]) -> float:
        """
        Calculate stop loss price for a trading signal.
        
        Args:
            signal: Trading signal
            market_data: Current market data
            
        Returns:
            float: Stop loss price
        """
        if signal.stop_loss:
            return signal.stop_loss
        
        if self.use_atr_stops:
            return self._calculate_atr_stop_loss(signal, market_data)
        else:
            return self._calculate_percentage_stop_loss(signal)
    
    def calculate_take_profit(self, signal: TradingSignal, stop_loss: float, 
                            market_data: Dict[str, Any]) -> float:
        """
        Calculate take profit price for a trading signal.
        
        Args:
            signal: Trading signal
            stop_loss: Stop loss price
            market_data: Current market data
            
        Returns:
            float: Take profit price
        """
        if signal.take_profit:
            return signal.take_profit
        
        # Calculate based on risk/reward ratio
        risk = abs(signal.price - stop_loss)
        reward = risk * self.risk_reward_ratio
        
        if signal.action == 'BUY':
            return signal.price + reward
        else:
            return signal.price - reward
    
    def _calculate_atr_stop_loss(self, signal: TradingSignal, market_data: Dict[str, Any]) -> float:
        """
        Calculate ATR-based stop loss.
        
        Args:
            signal: Trading signal
            market_data: Market data
            
        Returns:
            float: Stop loss price
        """
        # TODO: Implement ATR-based stop loss
        # 1. Get ATR value from market data
        # 2. Multiply by ATR multiplier
        # 3. Subtract/add from entry price based on direction
        
        atr = market_data.get('atr', 0.01)  # Default ATR
        atr_distance = atr * self.atr_multiplier
        
        if signal.action == 'BUY':
            return signal.price - atr_distance
        else:
            return signal.price + atr_distance
    
    def _calculate_percentage_stop_loss(self, signal: TradingSignal) -> float:
        """
        Calculate percentage-based stop loss.
        
        Args:
            signal: Trading signal
            
        Returns:
            float: Stop loss price
        """
        if signal.action == 'BUY':
            return signal.price * (1 - self.default_stop_loss_pct)
        else:
            return signal.price * (1 + self.default_stop_loss_pct)


class TradeLogger:
    """
    Handles trade logging to CSV and other formats.
    """
    
    def __init__(self, log_file: str = 'trade_log.csv'):
        """
        Initialize trade logger.
        
        Args:
            log_file: Path to the trade log CSV file
        """
        self.log_file = log_file
        self.logger = logging.getLogger(__name__)
        self._ensure_log_file_exists()
    
    def _ensure_log_file_exists(self):
        """Create log file with headers if it doesn't exist."""
        if not os.path.exists(self.log_file):
            headers = [
                'trade_id', 'symbol', 'strategy', 'action', 'entry_price', 'exit_price',
                'quantity', 'stop_loss', 'take_profit', 'entry_time', 'exit_time',
                'pnl', 'pnl_percentage', 'fees', 'status', 'timeframe', 'confidence', 'notes'
            ]
            
            with open(self.log_file, 'w', newline='') as file:
                writer = csv.writer(file)
                writer.writerow(headers)
    
    def log_trade(self, trade_record: TradeRecord):
        """
        Log a trade record to CSV file.
        
        Args:
            trade_record: Trade record to log
        """
        try:
            with open(self.log_file, 'a', newline='') as file:
                writer = csv.writer(file)
                
                # Convert trade record to list
                trade_data = [
                    trade_record.trade_id,
                    trade_record.symbol,
                    trade_record.strategy,
                    trade_record.action,
                    trade_record.entry_price,
                    trade_record.exit_price,
                    trade_record.quantity,
                    trade_record.stop_loss,
                    trade_record.take_profit,
                    trade_record.entry_time.isoformat() if trade_record.entry_time else '',
                    trade_record.exit_time.isoformat() if trade_record.exit_time else '',
                    trade_record.pnl,
                    trade_record.pnl_percentage,
                    trade_record.fees,
                    trade_record.status,
                    trade_record.timeframe,
                    trade_record.confidence,
                    trade_record.notes
                ]
                
                writer.writerow(trade_data)
                self.logger.info(f"Logged trade: {trade_record.trade_id}")
                
        except Exception as e:
            self.logger.error(f"Error logging trade: {e}")
    
    def get_trade_history(self, limit: Optional[int] = None) -> List[TradeRecord]:
        """
        Get trade history from log file.
        
        Args:
            limit: Maximum number of trades to return
            
        Returns:
            list: List of TradeRecord objects
        """
        trades = []
        
        try:
            with open(self.log_file, 'r') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    # Convert string dates back to datetime
                    if row['entry_time']:
                        row['entry_time'] = datetime.fromisoformat(row['entry_time'])
                    if row['exit_time']:
                        row['exit_time'] = datetime.fromisoformat(row['exit_time'])
                    
                    # Convert numeric fields
                    numeric_fields = ['entry_price', 'exit_price', 'quantity', 'stop_loss', 
                                    'take_profit', 'pnl', 'pnl_percentage', 'fees', 'confidence']
                    for field in numeric_fields:
                        if row[field]:
                            row[field] = float(row[field])
                        else:
                            row[field] = None if field in ['exit_price', 'stop_loss', 'take_profit'] else 0.0
                    
                    trades.append(TradeRecord(**row))
                    
                    if limit and len(trades) >= limit:
                        break
                        
        except Exception as e:
            self.logger.error(f"Error reading trade history: {e}")
        
        return trades


class TradeManager:
    """
    Main trade management class that orchestrates position sizing, risk management, and logging.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize trade manager.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.position_sizer = PositionSizer(config)
        self.risk_manager = RiskManager(config)
        self.trade_logger = TradeLogger(config.get('trade_log_file', 'trade_log.csv'))
        self.logger = logging.getLogger(__name__)
        self.active_trades = {}
    
    def execute_trade(self, signal: TradingSignal, account_balance: float = 10000, 
                     market_data: Dict[str, Any] = None) -> Optional[TradeRecord]:
        """
        Execute a trading signal.
        
        Args:
            signal: Trading signal to execute
            account_balance: Current account balance
            market_data: Current market data
            
        Returns:
            TradeRecord or None if trade was not executed
        """
        try:
            # Calculate position size
            position_size_pct = self.position_sizer.calculate_position_size(
                signal, account_balance, market_data or {}
            )
            
            # Calculate quantity
            quantity = (account_balance * position_size_pct) / signal.price
            
            # Calculate stop loss and take profit
            stop_loss = self.risk_manager.calculate_stop_loss(signal, market_data or {})
            take_profit = self.risk_manager.calculate_take_profit(signal, stop_loss, market_data or {})
            
            # Create trade record
            trade_id = f"{signal.symbol}_{signal.action}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            trade_record = TradeRecord(
                trade_id=trade_id,
                symbol=signal.symbol,
                strategy=signal.metadata.get('strategy', 'unknown'),
                action=signal.action,
                entry_price=signal.price,
                quantity=quantity,
                stop_loss=stop_loss,
                take_profit=take_profit,
                timeframe=signal.timeframe,
                confidence=signal.confidence,
                notes=f"Signal from {signal.metadata.get('strategy', 'unknown')} strategy"
            )
            
            # Log the trade
            self.trade_logger.log_trade(trade_record)
            
            # Add to active trades
            self.active_trades[trade_id] = trade_record
            
            self.logger.info(f"Executed trade: {trade_id}")
            return trade_record
            
        except Exception as e:
            self.logger.error(f"Error executing trade: {e}")
            return None
    
    def close_trade(self, trade_id: str, exit_price: float, fees: float = 0.0) -> bool:
        """
        Close an active trade.
        
        Args:
            trade_id: ID of the trade to close
            exit_price: Exit price
            fees: Trading fees
            
        Returns:
            bool: True if trade was closed successfully
        """
        if trade_id not in self.active_trades:
            self.logger.warning(f"Trade {trade_id} not found in active trades")
            return False
        
        try:
            trade = self.active_trades[trade_id]
            trade.exit_price = exit_price
            trade.exit_time = datetime.now()
            trade.fees = fees
            trade.status = 'CLOSED'
            
            # Calculate PnL
            if trade.action == 'BUY':
                trade.pnl = (exit_price - trade.entry_price) * trade.quantity - fees
            else:
                trade.pnl = (trade.entry_price - exit_price) * trade.quantity - fees
            
            trade.pnl_percentage = (trade.pnl / (trade.entry_price * trade.quantity)) * 100
            
            # Update log
            self.trade_logger.log_trade(trade)
            
            # Remove from active trades
            del self.active_trades[trade_id]
            
            self.logger.info(f"Closed trade: {trade_id}, PnL: {trade.pnl:.2f}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error closing trade {trade_id}: {e}")
            return False
