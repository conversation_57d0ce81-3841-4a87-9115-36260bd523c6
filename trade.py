"""
Trade Management Module

Handles trade calculations, position sizing, and logging.
Includes functions for:
- SL/TP calculation based on Risk:Reward ratio
- Position sizing based on capital and risk percent
- Trade logging to CSV file

No real order placement - focuses on calculations and logging.
"""

import csv
import os
from datetime import datetime
from typing import Dict, Any, Optional, List
import logging


def calculate_sl_tp(entry_price: float, action: str, risk_reward_ratio: float = 2.0,
                   risk_percent: float = 0.02) -> Dict[str, float]:
    """
    Calculate Stop Loss and Take Profit based on entry price and Risk:Reward ratio.

    Args:
        entry_price: Entry price for the trade
        action: 'BUY' or 'SELL'
        risk_reward_ratio: Risk to reward ratio (default 1:2)
        risk_percent: Risk percentage of entry price (default 2%)

    Returns:
        dict: Dictionary with 'stop_loss', 'take_profit', 'risk_amount', 'reward_amount'

    Example:
        >>> calculate_sl_tp(50000, 'BUY', 2.0, 0.02)
        {
            'stop_loss': 49000.0,
            'take_profit': 52000.0,
            'risk_amount': 1000.0,
            'reward_amount': 2000.0
        }
    """
    if entry_price <= 0:
        raise ValueError("Entry price must be positive")

    if risk_percent <= 0 or risk_percent >= 1:
        raise ValueError("Risk percent must be between 0 and 1")

    if risk_reward_ratio <= 0:
        raise ValueError("Risk reward ratio must be positive")

    action = action.upper()
    if action not in ['BUY', 'SELL']:
        raise ValueError("Action must be 'BUY' or 'SELL'")

    # Calculate risk amount (distance from entry to stop loss)
    risk_amount = entry_price * risk_percent

    # Calculate reward amount based on risk:reward ratio
    reward_amount = risk_amount * risk_reward_ratio

    if action == 'BUY':
        # For BUY: SL below entry, TP above entry
        stop_loss = entry_price - risk_amount
        take_profit = entry_price + reward_amount
    else:  # SELL
        # For SELL: SL above entry, TP below entry
        stop_loss = entry_price + risk_amount
        take_profit = entry_price - reward_amount

    return {
        'stop_loss': round(stop_loss, 4),
        'take_profit': round(take_profit, 4),
        'risk_amount': round(risk_amount, 4),
        'reward_amount': round(reward_amount, 4),
        'risk_reward_ratio': risk_reward_ratio
    }


def calculate_position_size(capital: float, risk_percent: float, entry_price: float,
                          stop_loss: float, action: str) -> Dict[str, float]:
    """
    Calculate position size based on capital, risk percentage, and stop loss distance.

    Args:
        capital: Total available capital
        risk_percent: Percentage of capital to risk (e.g., 0.02 for 2%)
        entry_price: Entry price for the trade
        stop_loss: Stop loss price
        action: 'BUY' or 'SELL'

    Returns:
        dict: Dictionary with position size details

    Example:
        >>> calculate_position_size(10000, 0.02, 50000, 49000, 'BUY')
        {
            'position_size_usd': 200.0,
            'quantity': 0.004,
            'risk_amount': 200.0,
            'risk_percent_actual': 0.02
        }
    """
    if capital <= 0:
        raise ValueError("Capital must be positive")

    if risk_percent <= 0 or risk_percent >= 1:
        raise ValueError("Risk percent must be between 0 and 1")

    if entry_price <= 0 or stop_loss <= 0:
        raise ValueError("Entry price and stop loss must be positive")

    action = action.upper()
    if action not in ['BUY', 'SELL']:
        raise ValueError("Action must be 'BUY' or 'SELL'")

    # Calculate risk amount in USD
    risk_amount_usd = capital * risk_percent

    # Calculate price difference between entry and stop loss
    price_diff = abs(entry_price - stop_loss)

    if price_diff == 0:
        raise ValueError("Entry price and stop loss cannot be the same")

    # Calculate position size in USD
    # Risk Amount = Position Size * (Price Difference / Entry Price)
    # Therefore: Position Size = Risk Amount / (Price Difference / Entry Price)
    position_size_usd = risk_amount_usd / (price_diff / entry_price)

    # Calculate quantity (how many units of the asset)
    quantity = position_size_usd / entry_price

    # Calculate actual risk percentage
    actual_risk_amount = quantity * price_diff
    actual_risk_percent = actual_risk_amount / capital

    return {
        'position_size_usd': round(position_size_usd, 2),
        'quantity': round(quantity, 8),  # 8 decimals for crypto precision
        'risk_amount': round(actual_risk_amount, 2),
        'risk_percent_actual': round(actual_risk_percent, 4),
        'price_difference': round(price_diff, 4)
    }


def log_trade_to_csv(trade_data: Dict[str, Any], csv_file: str = 'trade_log.csv') -> bool:
    """
    Log trade data to CSV file in append mode.

    Args:
        trade_data: Dictionary containing trade information
        csv_file: Path to CSV file (default: 'trade_log.csv')

    Returns:
        bool: True if logging successful, False otherwise

    Expected trade_data format:
        {
            'trade_id': 'BTCUSDT_BUY_20240115_143000',
            'timestamp': '2024-01-15 14:30:00',
            'symbol': 'BTCUSDT',
            'strategy': 'rsi_divergence',
            'action': 'BUY',
            'entry_price': 50000.0,
            'stop_loss': 49000.0,
            'take_profit': 52000.0,
            'quantity': 0.004,
            'position_size_usd': 200.0,
            'risk_amount': 200.0,
            'risk_percent': 0.02,
            'risk_reward_ratio': 2.0,
            'confidence': 0.85,
            'reason': 'Bullish RSI divergence',
            'timeframe': '1h',
            'status': 'OPEN'
        }
    """
    try:
        # Define CSV headers
        headers = [
            'trade_id', 'timestamp', 'symbol', 'strategy', 'action',
            'entry_price', 'stop_loss', 'take_profit', 'quantity',
            'position_size_usd', 'risk_amount', 'risk_percent',
            'risk_reward_ratio', 'confidence', 'reason', 'timeframe', 'status'
        ]

        # Check if file exists to determine if we need to write headers
        file_exists = os.path.exists(csv_file)

        # Open file in append mode
        with open(csv_file, 'a', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, fieldnames=headers)

            # Write headers if file is new
            if not file_exists:
                writer.writeheader()
                logging.info(f"Created new trade log file: {csv_file}")

            # Ensure all required fields are present with defaults
            row_data = {}
            for header in headers:
                row_data[header] = trade_data.get(header, '')

            # Write the trade data
            writer.writerow(row_data)

        logging.info(f"Trade logged successfully: {trade_data.get('trade_id', 'Unknown')}")
        return True

    except Exception as e:
        logging.error(f"Error logging trade to CSV: {e}")
        return False


def generate_trade_id(symbol: str, action: str, timestamp: Optional[datetime] = None) -> str:
    """
    Generate a unique trade ID.

    Args:
        symbol: Trading symbol (e.g., 'BTCUSDT')
        action: Trade action ('BUY' or 'SELL')
        timestamp: Timestamp for the trade (default: current time)

    Returns:
        str: Unique trade ID

    Example:
        >>> generate_trade_id('BTCUSDT', 'BUY')
        'BTCUSDT_BUY_20240115_143052'
    """
    if timestamp is None:
        timestamp = datetime.now()

    # Format: SYMBOL_ACTION_YYYYMMDD_HHMMSS
    time_str = timestamp.strftime('%Y%m%d_%H%M%S')
    trade_id = f"{symbol}_{action.upper()}_{time_str}"

    return trade_id


def create_trade_record(symbol: str, strategy: str, action: str, entry_price: float,
                       capital: float, risk_percent: float = 0.02,
                       risk_reward_ratio: float = 2.0, confidence: float = 0.0,
                       reason: str = '', timeframe: str = '1h') -> Dict[str, Any]:
    """
    Create a complete trade record with all calculations.

    Args:
        symbol: Trading symbol
        strategy: Strategy name
        action: 'BUY' or 'SELL'
        entry_price: Entry price
        capital: Available capital
        risk_percent: Risk percentage (default: 2%)
        risk_reward_ratio: Risk:Reward ratio (default: 1:2)
        confidence: Signal confidence (0-1)
        reason: Trade reason/signal description
        timeframe: Analysis timeframe

    Returns:
        dict: Complete trade record
    """
    timestamp = datetime.now()

    # Generate trade ID
    trade_id = generate_trade_id(symbol, action, timestamp)

    # Calculate SL/TP
    sl_tp = calculate_sl_tp(entry_price, action, risk_reward_ratio, risk_percent)

    # Calculate position size
    position_info = calculate_position_size(
        capital, risk_percent, entry_price, sl_tp['stop_loss'], action
    )

    # Create complete trade record
    trade_record = {
        'trade_id': trade_id,
        'timestamp': timestamp.strftime('%Y-%m-%d %H:%M:%S'),
        'symbol': symbol,
        'strategy': strategy,
        'action': action.upper(),
        'entry_price': entry_price,
        'stop_loss': sl_tp['stop_loss'],
        'take_profit': sl_tp['take_profit'],
        'quantity': position_info['quantity'],
        'position_size_usd': position_info['position_size_usd'],
        'risk_amount': position_info['risk_amount'],
        'risk_percent': risk_percent,
        'risk_reward_ratio': risk_reward_ratio,
        'confidence': confidence,
        'reason': reason,
        'timeframe': timeframe,
        'status': 'OPEN'
    }

    return trade_record


def process_trade_signal(signal_data: Dict[str, Any], capital: float,
                        risk_percent: float = 0.02) -> Dict[str, Any]:
    """
    Process a trading signal and create a complete trade record.

    Args:
        signal_data: Signal data from strategy
        capital: Available capital
        risk_percent: Risk percentage

    Returns:
        dict: Complete trade record or error information

    Expected signal_data format:
        {
            'action': 'BUY',
            'price': 50000.0,
            'stop_loss': 49000.0,  # Optional - will calculate if not provided
            'take_profit': 52000.0,  # Optional - will calculate if not provided
            'confidence': 0.85,
            'reason': 'Bullish RSI divergence',
            'metadata': {
                'strategy_name': 'rsi_divergence',
                'timeframe': '1h'
            }
        }
    """
    try:
        # Extract required data
        action = signal_data.get('action', '').upper()
        entry_price = signal_data.get('price', 0)
        confidence = signal_data.get('confidence', 0)
        reason = signal_data.get('reason', 'No reason provided')

        # Extract metadata
        metadata = signal_data.get('metadata', {})
        strategy = metadata.get('strategy_name', 'unknown')
        timeframe = metadata.get('timeframe', '1h')
        symbol = metadata.get('symbol', 'UNKNOWN')

        # Validate required fields
        if not action or action not in ['BUY', 'SELL']:
            return {'error': 'Invalid or missing action'}

        if entry_price <= 0:
            return {'error': 'Invalid entry price'}

        # Check if SL/TP are provided, otherwise calculate them
        provided_sl = signal_data.get('stop_loss')
        provided_tp = signal_data.get('take_profit')

        if provided_sl and provided_tp:
            # Use provided SL/TP
            stop_loss = provided_sl
            take_profit = provided_tp

            # Calculate risk/reward ratio from provided levels
            risk_amount = abs(entry_price - stop_loss)
            reward_amount = abs(take_profit - entry_price)
            risk_reward_ratio = reward_amount / risk_amount if risk_amount > 0 else 2.0
        else:
            # Calculate SL/TP using default risk/reward ratio
            sl_tp = calculate_sl_tp(entry_price, action, 2.0, risk_percent)
            stop_loss = sl_tp['stop_loss']
            take_profit = sl_tp['take_profit']
            risk_reward_ratio = 2.0

        # Calculate position size
        position_info = calculate_position_size(
            capital, risk_percent, entry_price, stop_loss, action
        )

        # Create trade record
        trade_record = create_trade_record(
            symbol=symbol,
            strategy=strategy,
            action=action,
            entry_price=entry_price,
            capital=capital,
            risk_percent=risk_percent,
            risk_reward_ratio=risk_reward_ratio,
            confidence=confidence,
            reason=reason,
            timeframe=timeframe
        )

        # Override calculated SL/TP if they were provided
        if provided_sl and provided_tp:
            trade_record['stop_loss'] = stop_loss
            trade_record['take_profit'] = take_profit
            trade_record['quantity'] = position_info['quantity']
            trade_record['position_size_usd'] = position_info['position_size_usd']
            trade_record['risk_amount'] = position_info['risk_amount']

        # Log to CSV
        log_success = log_trade_to_csv(trade_record)

        if not log_success:
            trade_record['warning'] = 'Trade processed but logging failed'

        return trade_record

    except Exception as e:
        logging.error(f"Error processing trade signal: {e}")
        return {'error': str(e)}


def read_trade_log(csv_file: str = 'trade_log.csv', limit: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    Read trades from CSV log file.

    Args:
        csv_file: Path to CSV file
        limit: Maximum number of trades to return (None for all)

    Returns:
        list: List of trade dictionaries
    """
    trades = []

    try:
        if not os.path.exists(csv_file):
            logging.warning(f"Trade log file not found: {csv_file}")
            return trades

        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)

            for row in reader:
                trades.append(dict(row))

                if limit and len(trades) >= limit:
                    break

        logging.info(f"Read {len(trades)} trades from {csv_file}")

    except Exception as e:
        logging.error(f"Error reading trade log: {e}")

    return trades


# Example usage and testing functions
def example_usage():
    """
    Example usage of the trade management functions.
    """
    print("=== Trade Management Example ===\n")

    # Example 1: Calculate SL/TP for a BUY signal
    print("1. Calculate SL/TP for BUY signal:")
    sl_tp = calculate_sl_tp(50000, 'BUY', 2.0, 0.02)
    print(f"   Entry: $50,000")
    print(f"   Stop Loss: ${sl_tp['stop_loss']}")
    print(f"   Take Profit: ${sl_tp['take_profit']}")
    print(f"   Risk/Reward: 1:{sl_tp['risk_reward_ratio']}")
    print()

    # Example 2: Calculate position size
    print("2. Calculate position size:")
    position = calculate_position_size(10000, 0.02, 50000, sl_tp['stop_loss'], 'BUY')
    print(f"   Capital: $10,000")
    print(f"   Risk: 2%")
    print(f"   Position Size: ${position['position_size_usd']}")
    print(f"   Quantity: {position['quantity']} BTC")
    print()

    # Example 3: Create and log a trade
    print("3. Create and log trade:")
    trade_record = create_trade_record(
        symbol='BTCUSDT',
        strategy='rsi_divergence',
        action='BUY',
        entry_price=50000,
        capital=10000,
        confidence=0.85,
        reason='Bullish RSI divergence detected'
    )

    print(f"   Trade ID: {trade_record['trade_id']}")
    print(f"   Position: ${trade_record['position_size_usd']} ({trade_record['quantity']} BTC)")
    print(f"   SL: ${trade_record['stop_loss']} | TP: ${trade_record['take_profit']}")

    # Log to CSV
    success = log_trade_to_csv(trade_record)
    print(f"   Logged to CSV: {'✅' if success else '❌'}")


if __name__ == "__main__":
    # Run example when script is executed directly
    example_usage()
