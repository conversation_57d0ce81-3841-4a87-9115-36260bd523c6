"""
n8n workflow integration for automated trading workflows.
"""

import requests
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from strategies.base_strategy import TradingSignal
from trade import TradeRecord


class N8NClient:
    """
    Client for integrating with n8n workflows.
    """
    
    def __init__(self, base_url: str, api_key: Optional[str] = None, config: Dict[str, Any] = None):
        """
        Initialize n8n client.
        
        Args:
            base_url: n8n instance base URL
            api_key: API key for authentication (if required)
            config: Additional configuration
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Setup session
        self.session = requests.Session()
        if api_key:
            self.session.headers.update({'Authorization': f'Bearer {api_key}'})
        
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'TradingBot-N8N-Client/1.0'
        })
    
    def trigger_signal_analysis_workflow(self, signal: TradingSignal, 
                                       market_data: Dict[str, Any],
                                       workflow_id: str) -> Dict[str, Any]:
        """
        Trigger n8n workflow for signal analysis.
        
        Args:
            signal: Trading signal
            market_data: Market data
            workflow_id: n8n workflow ID
            
        Returns:
            dict: Workflow execution result
        """
        try:
            # Prepare payload
            payload = {
                'signal': {
                    'symbol': signal.symbol,
                    'action': signal.action,
                    'price': signal.price,
                    'confidence': signal.confidence,
                    'timeframe': signal.timeframe,
                    'timestamp': signal.timestamp.isoformat(),
                    'metadata': signal.metadata
                },
                'market_data': market_data,
                'timestamp': datetime.now().isoformat()
            }
            
            # Trigger workflow
            response = self._trigger_workflow(workflow_id, payload)
            
            self.logger.info(f"Signal analysis workflow triggered: {workflow_id}")
            return response
            
        except Exception as e:
            self.logger.error(f"Error triggering signal analysis workflow: {e}")
            return {'error': str(e)}
    
    def trigger_trade_notification_workflow(self, trade_record: TradeRecord,
                                          workflow_id: str) -> Dict[str, Any]:
        """
        Trigger n8n workflow for trade notifications.
        
        Args:
            trade_record: Trade record
            workflow_id: n8n workflow ID
            
        Returns:
            dict: Workflow execution result
        """
        try:
            # Prepare payload
            payload = {
                'trade': {
                    'trade_id': trade_record.trade_id,
                    'symbol': trade_record.symbol,
                    'strategy': trade_record.strategy,
                    'action': trade_record.action,
                    'entry_price': trade_record.entry_price,
                    'exit_price': trade_record.exit_price,
                    'quantity': trade_record.quantity,
                    'pnl': trade_record.pnl,
                    'pnl_percentage': trade_record.pnl_percentage,
                    'status': trade_record.status,
                    'entry_time': trade_record.entry_time.isoformat() if trade_record.entry_time else None,
                    'exit_time': trade_record.exit_time.isoformat() if trade_record.exit_time else None
                },
                'timestamp': datetime.now().isoformat()
            }
            
            # Trigger workflow
            response = self._trigger_workflow(workflow_id, payload)
            
            self.logger.info(f"Trade notification workflow triggered: {workflow_id}")
            return response
            
        except Exception as e:
            self.logger.error(f"Error triggering trade notification workflow: {e}")
            return {'error': str(e)}
    
    def trigger_portfolio_analysis_workflow(self, trades: List[TradeRecord],
                                          account_balance: float,
                                          workflow_id: str) -> Dict[str, Any]:
        """
        Trigger n8n workflow for portfolio analysis.
        
        Args:
            trades: List of trades
            account_balance: Current account balance
            workflow_id: n8n workflow ID
            
        Returns:
            dict: Workflow execution result
        """
        try:
            # Prepare payload
            trades_data = []
            for trade in trades:
                trades_data.append({
                    'trade_id': trade.trade_id,
                    'symbol': trade.symbol,
                    'strategy': trade.strategy,
                    'action': trade.action,
                    'pnl': trade.pnl,
                    'pnl_percentage': trade.pnl_percentage,
                    'entry_time': trade.entry_time.isoformat() if trade.entry_time else None,
                    'exit_time': trade.exit_time.isoformat() if trade.exit_time else None
                })
            
            payload = {
                'portfolio': {
                    'account_balance': account_balance,
                    'trades': trades_data,
                    'total_trades': len(trades),
                    'total_pnl': sum(t.pnl for t in trades if t.pnl)
                },
                'timestamp': datetime.now().isoformat()
            }
            
            # Trigger workflow
            response = self._trigger_workflow(workflow_id, payload)
            
            self.logger.info(f"Portfolio analysis workflow triggered: {workflow_id}")
            return response
            
        except Exception as e:
            self.logger.error(f"Error triggering portfolio analysis workflow: {e}")
            return {'error': str(e)}
    
    def trigger_market_alert_workflow(self, alert_data: Dict[str, Any],
                                    workflow_id: str) -> Dict[str, Any]:
        """
        Trigger n8n workflow for market alerts.
        
        Args:
            alert_data: Alert data
            workflow_id: n8n workflow ID
            
        Returns:
            dict: Workflow execution result
        """
        try:
            # Prepare payload
            payload = {
                'alert': alert_data,
                'timestamp': datetime.now().isoformat()
            }
            
            # Trigger workflow
            response = self._trigger_workflow(workflow_id, payload)
            
            self.logger.info(f"Market alert workflow triggered: {workflow_id}")
            return response
            
        except Exception as e:
            self.logger.error(f"Error triggering market alert workflow: {e}")
            return {'error': str(e)}
    
    def _trigger_workflow(self, workflow_id: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Trigger an n8n workflow with payload.
        
        Args:
            workflow_id: Workflow ID
            payload: Data payload
            
        Returns:
            dict: Response data
        """
        url = f"{self.base_url}/webhook/{workflow_id}"
        
        try:
            response = self.session.post(url, json=payload, timeout=30)
            response.raise_for_status()
            
            return response.json() if response.content else {'status': 'success'}
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error triggering workflow {workflow_id}: {e}")
            raise
    
    def get_workflow_status(self, execution_id: str) -> Dict[str, Any]:
        """
        Get status of a workflow execution.
        
        Args:
            execution_id: Execution ID
            
        Returns:
            dict: Execution status
        """
        url = f"{self.base_url}/api/v1/executions/{execution_id}"
        
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error getting workflow status: {e}")
            return {'error': str(e)}
    
    def list_workflows(self) -> List[Dict[str, Any]]:
        """
        List available workflows.
        
        Returns:
            list: List of workflows
        """
        url = f"{self.base_url}/api/v1/workflows"
        
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            return response.json().get('data', [])
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error listing workflows: {e}")
            return []


class N8NWorkflowManager:
    """
    Manages n8n workflow integrations for the trading bot.
    """
    
    def __init__(self, n8n_client: N8NClient, config: Dict[str, Any] = None):
        """
        Initialize workflow manager.
        
        Args:
            n8n_client: n8n client instance
            config: Configuration dictionary
        """
        self.n8n_client = n8n_client
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Workflow IDs from config
        self.workflows = {
            'signal_analysis': self.config.get('signal_analysis_workflow_id'),
            'trade_notification': self.config.get('trade_notification_workflow_id'),
            'portfolio_analysis': self.config.get('portfolio_analysis_workflow_id'),
            'market_alert': self.config.get('market_alert_workflow_id'),
            'risk_management': self.config.get('risk_management_workflow_id'),
            'performance_report': self.config.get('performance_report_workflow_id')
        }
    
    def process_trading_signal(self, signal: TradingSignal, 
                             market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process trading signal through n8n workflows.
        
        Args:
            signal: Trading signal
            market_data: Market data
            
        Returns:
            dict: Processing results
        """
        results = {}
        
        # Signal analysis workflow
        if self.workflows['signal_analysis']:
            try:
                analysis_result = self.n8n_client.trigger_signal_analysis_workflow(
                    signal, market_data, self.workflows['signal_analysis']
                )
                results['signal_analysis'] = analysis_result
            except Exception as e:
                self.logger.error(f"Error in signal analysis workflow: {e}")
                results['signal_analysis'] = {'error': str(e)}
        
        # Risk management workflow
        if self.workflows['risk_management']:
            try:
                risk_result = self.n8n_client.trigger_workflow(
                    self.workflows['risk_management'],
                    {'signal': signal.__dict__, 'market_data': market_data}
                )
                results['risk_management'] = risk_result
            except Exception as e:
                self.logger.error(f"Error in risk management workflow: {e}")
                results['risk_management'] = {'error': str(e)}
        
        return results
    
    def process_trade_execution(self, trade_record: TradeRecord) -> Dict[str, Any]:
        """
        Process trade execution through n8n workflows.
        
        Args:
            trade_record: Trade record
            
        Returns:
            dict: Processing results
        """
        results = {}
        
        # Trade notification workflow
        if self.workflows['trade_notification']:
            try:
                notification_result = self.n8n_client.trigger_trade_notification_workflow(
                    trade_record, self.workflows['trade_notification']
                )
                results['trade_notification'] = notification_result
            except Exception as e:
                self.logger.error(f"Error in trade notification workflow: {e}")
                results['trade_notification'] = {'error': str(e)}
        
        return results
    
    def process_portfolio_update(self, trades: List[TradeRecord], 
                               account_balance: float) -> Dict[str, Any]:
        """
        Process portfolio update through n8n workflows.
        
        Args:
            trades: List of trades
            account_balance: Account balance
            
        Returns:
            dict: Processing results
        """
        results = {}
        
        # Portfolio analysis workflow
        if self.workflows['portfolio_analysis']:
            try:
                analysis_result = self.n8n_client.trigger_portfolio_analysis_workflow(
                    trades, account_balance, self.workflows['portfolio_analysis']
                )
                results['portfolio_analysis'] = analysis_result
            except Exception as e:
                self.logger.error(f"Error in portfolio analysis workflow: {e}")
                results['portfolio_analysis'] = {'error': str(e)}
        
        # Performance report workflow
        if self.workflows['performance_report']:
            try:
                report_result = self.n8n_client.trigger_workflow(
                    self.workflows['performance_report'],
                    {
                        'trades': [t.__dict__ for t in trades],
                        'account_balance': account_balance,
                        'timestamp': datetime.now().isoformat()
                    }
                )
                results['performance_report'] = report_result
            except Exception as e:
                self.logger.error(f"Error in performance report workflow: {e}")
                results['performance_report'] = {'error': str(e)}
        
        return results
    
    def send_market_alert(self, alert_type: str, message: str, 
                         data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Send market alert through n8n workflow.
        
        Args:
            alert_type: Type of alert
            message: Alert message
            data: Additional data
            
        Returns:
            dict: Processing results
        """
        if not self.workflows['market_alert']:
            return {'error': 'Market alert workflow not configured'}
        
        alert_data = {
            'type': alert_type,
            'message': message,
            'data': data or {},
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            result = self.n8n_client.trigger_market_alert_workflow(
                alert_data, self.workflows['market_alert']
            )
            return result
        except Exception as e:
            self.logger.error(f"Error sending market alert: {e}")
            return {'error': str(e)}
    
    def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on n8n workflows.
        
        Returns:
            dict: Health check results
        """
        results = {
            'status': 'healthy',
            'workflows': {},
            'timestamp': datetime.now().isoformat()
        }
        
        # Check each configured workflow
        for workflow_name, workflow_id in self.workflows.items():
            if workflow_id:
                try:
                    # Simple ping to check if workflow exists
                    # This would depend on n8n API capabilities
                    results['workflows'][workflow_name] = {
                        'id': workflow_id,
                        'status': 'available'
                    }
                except Exception as e:
                    results['workflows'][workflow_name] = {
                        'id': workflow_id,
                        'status': 'error',
                        'error': str(e)
                    }
                    results['status'] = 'degraded'
        
        return results
