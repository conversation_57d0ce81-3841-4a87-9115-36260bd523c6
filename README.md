# AI-Powered Crypto Trading Bot

A modular, AI-powered cryptocurrency trading bot built in Python with clean architecture and comprehensive features.

## 📁 Project Structure

```
trading-bot/
├── bot.py                      # Main bot entry point and orchestration
├── strategy_control.py         # Strategy enable/disable flags and parameters
├── strategy_loader.py          # Dynamic strategy loading system
├── trade.py                    # Trade management, position sizing, and logging
├── notify.py                   # Telegram notifications and chart generation
├── utils.py                    # Technical indicators and utility functions
├── prompt_formatter.py         # AI prompt generation for analysis
├── trade_log.csv              # CSV file for trade logging
├── README.md                   # This file
│
├── strategies/                 # Trading strategies directory
│   ├── __init__.py
│   ├── base_strategy.py        # Base strategy class and signal structure
│   ├── rsi_divergence.py       # RSI divergence strategy
│   ├── bollinger_bands.py      # Bollinger Bands strategy
│   └── moving_average_crossover.py # MA crossover strategy
│
├── database/                   # Database integration (optional)
│   ├── __init__.py
│   ├── models.py              # SQLAlchemy models
│   └── connection.py          # Database connection management
│
├── backtest_engine/           # Backtesting system (optional)
│   ├── __init__.py
│   ├── runner.py              # Backtest execution engine
│   ├── metrics.py             # Performance metrics calculation
│   └── visualizer.py          # Backtest result visualization
│
└── ai_agent/                  # AI integration (optional)
    ├── __init__.py
    ├── openai_client.py       # OpenAI/ChatGPT integration
    └── n8n_integration.py     # n8n workflow automation
```

## 🚀 Features

### Core Components

- **Modular Strategy System**: Easy to add/remove trading strategies
- **Dynamic Strategy Loading**: Strategies loaded based on control flags
- **Comprehensive Trade Management**: Position sizing, SL/TP, and logging
- **Telegram Integration**: Real-time notifications with charts
- **Technical Indicators**: RSI, MACD, Bollinger Bands, ATR, and more
- **AI-Powered Analysis**: Integration with OpenAI for trade analysis

### Optional Components

- **Database Support**: SQLite/PostgreSQL for advanced logging
- **Backtesting Engine**: Test strategies on historical data
- **Performance Visualization**: Charts and metrics for analysis
- **n8n Integration**: Workflow automation and external integrations

## 🛠️ Setup and Installation

### Prerequisites

```bash
pip install numpy pandas matplotlib seaborn requests sqlalchemy openai
```

### Basic Configuration

1. **Configure API Keys** in `bot.py`:
```python
config = {
    'exchange': 'binance',
    'api_key': 'your_api_key',
    'api_secret': 'your_api_secret',
    'telegram_token': 'your_telegram_token',
    'telegram_chat_id': 'your_chat_id'
}
```

2. **Enable/Disable Strategies** in `strategy_control.py`:
```python
STRATEGY_FLAGS = {
    'rsi_divergence': True,
    'bollinger_bands': True,
    'moving_average_crossover': False,
    # ... other strategies
}
```

3. **Run the Bot**:
```bash
python bot.py
```

## 📊 Strategy Management

### Adding New Strategies

1. Create a new file in `strategies/` directory
2. Inherit from `BaseStrategy` class
3. Implement required methods:
   - `analyze()`: Generate trading signals
   - `validate_signal()`: Validate signals before execution
4. Add strategy to `strategy_control.py`

### Example Strategy Structure

```python
from .base_strategy import BaseStrategy, TradingSignal

class MyStrategy(BaseStrategy):
    def analyze(self, market_data):
        # Strategy logic here
        signals = []
        # Generate signals based on analysis
        return signals
    
    def validate_signal(self, signal, market_data):
        # Validation logic
        return True
```

## 🔧 Configuration Options

### Strategy Control (`strategy_control.py`)

- **STRATEGY_FLAGS**: Enable/disable individual strategies
- **STRATEGY_PARAMS**: Configure strategy-specific parameters

### Trade Management (`trade.py`)

- **Position Sizing**: Risk-based position calculation
- **Risk Management**: Stop loss and take profit calculation
- **Trade Logging**: CSV and database logging options

### Notifications (`notify.py`)

- **Telegram Integration**: Real-time trade notifications
- **Chart Generation**: Automatic chart creation and sending
- **Custom Messages**: Flexible notification formatting

## 🤖 AI Integration

### OpenAI Integration

```python
from ai_agent.openai_client import OpenAIClient

client = OpenAIClient(api_key="your_openai_key")
analysis = client.analyze_trading_signal(signal, market_data)
```

### n8n Workflow Integration

```python
from ai_agent.n8n_integration import N8NClient

n8n = N8NClient(base_url="http://your-n8n-instance")
n8n.trigger_signal_analysis_workflow(signal, market_data, workflow_id)
```

## 📈 Backtesting

### Running Backtests

```python
from backtest_engine.runner import BacktestRunner
from strategies.rsi_divergence import RsiDivergence

runner = BacktestRunner(config)
strategy = RsiDivergence(params)
results = runner.run_backtest(strategy, "BTCUSDT", start_date, end_date)
```

### Performance Analysis

```python
from backtest_engine.metrics import BacktestAnalyzer

analyzer = BacktestAnalyzer()
analysis = analyzer.analyze_backtest(results)
```

## 📊 Database Integration

### Setup Database

```python
from database.connection import get_database_manager

db = get_database_manager("sqlite:///trading_bot.db")
db.create_tables()
```

### Logging Trades

```python
from database.models import Trade

# Trades are automatically logged to database if configured
```

## 🔍 Monitoring and Alerts

### Telegram Notifications

- Trade execution alerts
- Signal generation notifications
- Daily performance summaries
- Custom alert messages

### Performance Tracking

- Real-time P&L tracking
- Strategy performance metrics
- Risk management monitoring
- Equity curve visualization

## 🛡️ Risk Management

### Built-in Risk Controls

- **Position Sizing**: Automatic calculation based on account risk
- **Stop Loss**: ATR-based or percentage-based stops
- **Take Profit**: Risk/reward ratio optimization
- **Maximum Positions**: Limit concurrent trades
- **Volatility Adjustment**: Position size based on market volatility

### Risk Parameters

```python
config = {
    'max_risk_per_trade': 0.02,  # 2% risk per trade
    'max_position_size': 0.1,    # 10% max position size
    'risk_reward_ratio': 2.0,    # 1:2 risk/reward
    'use_atr_stops': True,       # Use ATR for stop loss
    'atr_multiplier': 2.0        # ATR multiplier for stops
}
```

## 🔄 Workflow Integration

### n8n Workflows

- **Signal Analysis**: Automated signal validation
- **Trade Notifications**: Multi-channel notifications
- **Portfolio Analysis**: Regular performance reviews
- **Market Alerts**: Custom market condition alerts
- **Risk Management**: Automated risk checks

### Custom Integrations

The modular architecture allows easy integration with:
- Discord bots
- Slack notifications
- Email alerts
- Custom APIs
- External databases
- Cloud services

## 📝 Logging and Monitoring

### Trade Logging

- **CSV Format**: Simple trade log in `trade_log.csv`
- **Database**: Advanced logging with SQLAlchemy models
- **Custom Fields**: Extensible logging structure

### Performance Monitoring

- **Real-time Metrics**: Live performance tracking
- **Historical Analysis**: Long-term performance trends
- **Strategy Comparison**: Compare multiple strategies
- **Risk Metrics**: Drawdown, Sharpe ratio, etc.

## 🚨 Important Notes

### Security

- Never commit API keys to version control
- Use environment variables for sensitive data
- Implement proper error handling
- Monitor for unusual activity

### Testing

- Always test strategies on paper trading first
- Use backtesting to validate strategy performance
- Start with small position sizes
- Monitor performance closely

### Compliance

- Ensure compliance with local trading regulations
- Understand tax implications of automated trading
- Keep detailed records of all trades
- Consider regulatory requirements

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add your strategy or improvement
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is for educational purposes. Use at your own risk. Trading cryptocurrencies involves substantial risk of loss.

## 🆘 Support

For questions and support:
- Check the documentation in each module
- Review example strategies
- Test with paper trading first
- Start with conservative settings

---

**Disclaimer**: This trading bot is for educational and research purposes only. Cryptocurrency trading involves substantial risk of loss. Past performance does not guarantee future results. Always do your own research and never invest more than you can afford to lose.
