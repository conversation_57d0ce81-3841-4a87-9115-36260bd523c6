"""
Visualization tools for backtest results and performance analysis.
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime
import io

from trade import TradeRecord


class BacktestVisualizer:
    """
    Creates visualizations for backtest results and performance analysis.
    """
    
    def __init__(self, style: str = 'seaborn-v0_8'):
        """
        Initialize backtest visualizer.
        
        Args:
            style: Matplotlib style to use
        """
        self.style = style
        plt.style.use(style)
        sns.set_palette("husl")
    
    def create_equity_curve_plot(self, equity_curve: List[Dict[str, Any]], 
                               title: str = "Equity Curve") -> bytes:
        """
        Create equity curve visualization.
        
        Args:
            equity_curve: List of equity values over time
            title: Plot title
            
        Returns:
            bytes: Plot image as bytes
        """
        if not equity_curve:
            return b''
        
        fig, ax = plt.subplots(figsize=(12, 6))
        
        timestamps = [point['timestamp'] for point in equity_curve]
        equity_values = [point['equity'] for point in equity_curve]
        
        ax.plot(timestamps, equity_values, linewidth=2, color='blue', label='Portfolio Value')
        ax.fill_between(timestamps, equity_values, alpha=0.3, color='blue')
        
        ax.set_title(title, fontsize=16, fontweight='bold')
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('Portfolio Value ($)', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # Format x-axis
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax.xaxis.set_major_locator(mdates.MonthLocator())
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        # Save to bytes
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
        buffer.seek(0)
        image_data = buffer.getvalue()
        buffer.close()
        plt.close(fig)
        
        return image_data
    
    def create_drawdown_plot(self, equity_curve: List[Dict[str, Any]], 
                           title: str = "Drawdown Analysis") -> bytes:
        """
        Create drawdown visualization.
        
        Args:
            equity_curve: List of equity values over time
            title: Plot title
            
        Returns:
            bytes: Plot image as bytes
        """
        if not equity_curve:
            return b''
        
        # Calculate drawdowns
        timestamps = [point['timestamp'] for point in equity_curve]
        equity_values = [point['equity'] for point in equity_curve]
        
        peak = equity_values[0]
        drawdowns = []
        
        for equity in equity_values:
            if equity > peak:
                peak = equity
            
            drawdown = (peak - equity) / peak * 100
            drawdowns.append(-drawdown)  # Negative for visualization
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), sharex=True)
        
        # Equity curve
        ax1.plot(timestamps, equity_values, linewidth=2, color='blue', label='Portfolio Value')
        ax1.set_title('Portfolio Value', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Value ($)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # Drawdown
        ax2.fill_between(timestamps, drawdowns, 0, alpha=0.7, color='red', label='Drawdown')
        ax2.plot(timestamps, drawdowns, linewidth=1, color='darkred')
        ax2.set_title('Drawdown', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Date', fontsize=12)
        ax2.set_ylabel('Drawdown (%)', fontsize=12)
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # Format x-axis
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax2.xaxis.set_major_locator(mdates.MonthLocator())
        plt.xticks(rotation=45)
        
        plt.suptitle(title, fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        # Save to bytes
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
        buffer.seek(0)
        image_data = buffer.getvalue()
        buffer.close()
        plt.close(fig)
        
        return image_data
    
    def create_returns_distribution(self, returns: List[float], 
                                  title: str = "Returns Distribution") -> bytes:
        """
        Create returns distribution visualization.
        
        Args:
            returns: List of period returns
            title: Plot title
            
        Returns:
            bytes: Plot image as bytes
        """
        if not returns:
            return b''
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Histogram
        ax1.hist(returns, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.axvline(np.mean(returns), color='red', linestyle='--', linewidth=2, label=f'Mean: {np.mean(returns):.4f}')
        ax1.axvline(np.median(returns), color='green', linestyle='--', linewidth=2, label=f'Median: {np.median(returns):.4f}')
        ax1.set_title('Returns Histogram', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Returns', fontsize=12)
        ax1.set_ylabel('Frequency', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # Q-Q plot
        from scipy import stats
        stats.probplot(returns, dist="norm", plot=ax2)
        ax2.set_title('Q-Q Plot (Normal Distribution)', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)
        
        plt.suptitle(title, fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        # Save to bytes
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
        buffer.seek(0)
        image_data = buffer.getvalue()
        buffer.close()
        plt.close(fig)
        
        return image_data
    
    def create_trade_analysis_plot(self, trades: List[TradeRecord], 
                                 title: str = "Trade Analysis") -> bytes:
        """
        Create trade analysis visualization.
        
        Args:
            trades: List of completed trades
            title: Plot title
            
        Returns:
            bytes: Plot image as bytes
        """
        if not trades:
            return b''
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # P&L distribution
        pnl_values = [trade.pnl for trade in trades]
        colors = ['green' if pnl > 0 else 'red' for pnl in pnl_values]
        
        ax1.bar(range(len(pnl_values)), pnl_values, color=colors, alpha=0.7)
        ax1.axhline(y=0, color='black', linestyle='-', linewidth=1)
        ax1.set_title('Trade P&L', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Trade Number', fontsize=12)
        ax1.set_ylabel('P&L ($)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        
        # P&L histogram
        ax2.hist(pnl_values, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.axvline(np.mean(pnl_values), color='red', linestyle='--', linewidth=2, 
                   label=f'Mean: ${np.mean(pnl_values):.2f}')
        ax2.set_title('P&L Distribution', fontsize=14, fontweight='bold')
        ax2.set_xlabel('P&L ($)', fontsize=12)
        ax2.set_ylabel('Frequency', fontsize=12)
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # Trade duration analysis
        durations = []
        for trade in trades:
            if trade.entry_time and trade.exit_time:
                duration = (trade.exit_time - trade.entry_time).total_seconds() / 3600
                durations.append(duration)
        
        if durations:
            ax3.hist(durations, bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
            ax3.axvline(np.mean(durations), color='red', linestyle='--', linewidth=2,
                       label=f'Mean: {np.mean(durations):.1f}h')
            ax3.set_title('Trade Duration Distribution', fontsize=14, fontweight='bold')
            ax3.set_xlabel('Duration (hours)', fontsize=12)
            ax3.set_ylabel('Frequency', fontsize=12)
            ax3.grid(True, alpha=0.3)
            ax3.legend()
        
        # Win/Loss analysis
        winning_trades = len([t for t in trades if t.pnl > 0])
        losing_trades = len([t for t in trades if t.pnl < 0])
        
        labels = ['Winning Trades', 'Losing Trades']
        sizes = [winning_trades, losing_trades]
        colors_pie = ['green', 'red']
        
        ax4.pie(sizes, labels=labels, colors=colors_pie, autopct='%1.1f%%', startangle=90)
        ax4.set_title('Win/Loss Ratio', fontsize=14, fontweight='bold')
        
        plt.suptitle(title, fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        # Save to bytes
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
        buffer.seek(0)
        image_data = buffer.getvalue()
        buffer.close()
        plt.close(fig)
        
        return image_data
    
    def create_strategy_comparison(self, strategy_results: Dict[str, Dict[str, Any]], 
                                 title: str = "Strategy Comparison") -> bytes:
        """
        Create strategy comparison visualization.
        
        Args:
            strategy_results: Dictionary of strategy results
            title: Plot title
            
        Returns:
            bytes: Plot image as bytes
        """
        if not strategy_results:
            return b''
        
        strategies = list(strategy_results.keys())
        metrics = ['total_return', 'sharpe_ratio', 'max_drawdown', 'win_rate']
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.flatten()
        
        for i, metric in enumerate(metrics):
            values = [strategy_results[strategy].get(metric, 0) for strategy in strategies]
            
            bars = axes[i].bar(strategies, values, alpha=0.7, color=plt.cm.Set3(np.linspace(0, 1, len(strategies))))
            axes[i].set_title(f'{metric.replace("_", " ").title()}', fontsize=14, fontweight='bold')
            axes[i].set_ylabel(metric.replace("_", " ").title(), fontsize=12)
            axes[i].grid(True, alpha=0.3)
            
            # Add value labels on bars
            for bar, value in zip(bars, values):
                height = bar.get_height()
                axes[i].text(bar.get_x() + bar.get_width()/2., height,
                           f'{value:.2f}', ha='center', va='bottom')
            
            plt.setp(axes[i].xaxis.get_majorticklabels(), rotation=45)
        
        plt.suptitle(title, fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        # Save to bytes
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
        buffer.seek(0)
        image_data = buffer.getvalue()
        buffer.close()
        plt.close(fig)
        
        return image_data
    
    def create_monthly_returns_heatmap(self, monthly_returns: Dict[str, float], 
                                     title: str = "Monthly Returns Heatmap") -> bytes:
        """
        Create monthly returns heatmap.
        
        Args:
            monthly_returns: Dictionary of monthly returns
            title: Plot title
            
        Returns:
            bytes: Plot image as bytes
        """
        # TODO: Implement monthly returns heatmap
        # This would create a calendar-style heatmap showing monthly performance
        
        fig, ax = plt.subplots(figsize=(12, 8))
        ax.text(0.5, 0.5, 'Monthly Returns Heatmap\n(To be implemented)', 
               ha='center', va='center', fontsize=16, transform=ax.transAxes)
        ax.set_title(title, fontsize=16, fontweight='bold')
        
        # Save to bytes
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
        buffer.seek(0)
        image_data = buffer.getvalue()
        buffer.close()
        plt.close(fig)
        
        return image_data
    
    def create_comprehensive_report(self, backtest_results: Dict[str, Any], 
                                  analysis_results: Dict[str, Any]) -> List[bytes]:
        """
        Create comprehensive backtest report with multiple visualizations.
        
        Args:
            backtest_results: Backtest results
            analysis_results: Analysis results
            
        Returns:
            list: List of plot images as bytes
        """
        plots = []
        
        # Equity curve
        equity_curve = backtest_results.get('equity_curve', [])
        if equity_curve:
            plots.append(self.create_equity_curve_plot(equity_curve))
            plots.append(self.create_drawdown_plot(equity_curve))
        
        # Trade analysis
        trades = backtest_results.get('trades', [])
        if trades:
            plots.append(self.create_trade_analysis_plot(trades))
        
        # Returns analysis
        if equity_curve and len(equity_curve) > 1:
            returns = []
            for i in range(1, len(equity_curve)):
                prev_equity = equity_curve[i - 1]['equity']
                curr_equity = equity_curve[i]['equity']
                if prev_equity != 0:
                    ret = (curr_equity - prev_equity) / prev_equity
                    returns.append(ret)
            
            if returns:
                plots.append(self.create_returns_distribution(returns))
        
        return plots
