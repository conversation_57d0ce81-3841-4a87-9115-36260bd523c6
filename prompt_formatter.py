"""
Prompt Formatter Module

Converts trading signals and data into structured, human-readable prompts
suitable for AI analysis and confirmation via ChatGPT or OpenAI API.

Functions:
- format_prompt(trade): Main function to format trade signals
- format_market_analysis_prompt(): Format market analysis requests
- validate_trade_data(): Validate and clean trade data

Usage:
    trade_data = {...}  # Strategy output
    prompt = format_prompt(trade_data)
    # Send prompt to OpenAI API for confirmation
"""

from typing import Dict, Any, Optional, List
from datetime import datetime


def format_prompt(trade: Dict[str, Any]) -> str:
    """
    Format a trading signal into a structured prompt for AI analysis.
    
    Takes the output from a trading strategy and converts it into a human-readable,
    structured prompt suitable for sending to ChatGPT or OpenAI API for confirmation.
    
    Args:
        trade: Dictionary containing trade signal data with keys:
            - 'action': 'BUY' or 'SELL'
            - 'price': Entry price (float)
            - 'stop_loss': Stop loss price (float)
            - 'take_profit': Take profit price (float)
            - 'confidence': Confidence score (0-1)
            - 'reason': Signal reasoning (string)
            - 'metadata': Additional data including strategy_name, symbol, timeframe
            
    Returns:
        str: Formatted prompt ready for AI analysis
        
    Example:
        >>> trade_data = {
        ...     'action': 'BUY',
        ...     'price': 50000.0,
        ...     'stop_loss': 49000.0,
        ...     'take_profit': 52000.0,
        ...     'confidence': 0.85,
        ...     'reason': 'Bullish RSI divergence detected',
        ...     'metadata': {
        ...         'strategy_name': 'rsi_divergence',
        ...         'symbol': 'BTCUSDT',
        ...         'timeframe': '1h'
        ...     }
        ... }
        >>> prompt = format_prompt(trade_data)
        >>> print(prompt)
    """
    # Extract trade data with defaults
    action = trade.get('action', 'UNKNOWN').upper()
    entry_price = trade.get('price', 0.0)
    stop_loss = trade.get('stop_loss', 0.0)
    take_profit = trade.get('take_profit', 0.0)
    confidence = trade.get('confidence', 0.0)
    reason = trade.get('reason', 'No reason provided')
    
    # Extract metadata
    metadata = trade.get('metadata', {})
    strategy_name = metadata.get('strategy_name', 'unknown_strategy')
    symbol = metadata.get('symbol', 'UNKNOWN')
    timeframe = metadata.get('timeframe', '1h')
    
    # Calculate risk/reward metrics
    risk_amount = abs(entry_price - stop_loss) if stop_loss else 0
    reward_amount = abs(take_profit - entry_price) if take_profit else 0
    risk_reward_ratio = reward_amount / risk_amount if risk_amount > 0 else 0
    
    # Calculate percentages
    risk_percent = (risk_amount / entry_price * 100) if entry_price > 0 else 0
    reward_percent = (reward_amount / entry_price * 100) if entry_price > 0 else 0
    
    # Format confidence as percentage
    confidence_percent = confidence * 100 if confidence <= 1 else confidence
    
    # Create structured prompt
    prompt = f"""# Trading Signal Analysis Request

## 📊 SIGNAL OVERVIEW
**Strategy:** {strategy_name.replace('_', ' ').title()}
**Symbol:** {symbol}
**Timeframe:** {timeframe}
**Action:** {action}
**Timestamp:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}

## 💰 TRADE PARAMETERS
**Entry Price:** ${entry_price:,.4f}
**Stop Loss:** ${stop_loss:,.4f}
**Take Profit:** ${take_profit:,.4f}

## 📈 RISK/REWARD ANALYSIS
**Risk Amount:** ${risk_amount:,.4f} ({risk_percent:.2f}%)
**Reward Amount:** ${reward_amount:,.4f} ({reward_percent:.2f}%)
**Risk/Reward Ratio:** 1:{risk_reward_ratio:.2f}

## 🎯 SIGNAL DETAILS
**Strategy Confidence:** {confidence_percent:.1f}%
**Signal Reasoning:** {reason}

## 🤖 AI ANALYSIS REQUEST

As an expert trading analyst, please provide a comprehensive analysis of this trading signal:

### 1. TECHNICAL VALIDATION
- Evaluate the technical setup and signal quality
- Assess if the entry, stop loss, and take profit levels are appropriate
- Comment on the risk/reward ratio (is 1:{risk_reward_ratio:.2f} acceptable?)

### 2. MARKET CONTEXT ANALYSIS
- Consider current market conditions for {symbol}
- Evaluate if this is a good time to enter this trade
- Assess any potential market risks or catalysts

### 3. STRATEGY ASSESSMENT
- Analyze the "{strategy_name}" strategy approach
- Comment on the {confidence_percent:.1f}% confidence level
- Evaluate the reasoning: "{reason}"

### 4. RISK MANAGEMENT REVIEW
- Is the {risk_percent:.2f}% risk level appropriate?
- Are the stop loss and take profit levels well-placed?
- Any suggestions for position sizing or risk adjustments?

### 5. FINAL RECOMMENDATION
Please provide one of the following recommendations:
- **EXECUTE**: Trade signal is strong, execute as planned
- **MODIFY**: Good signal but needs adjustments (specify what)
- **REJECT**: Signal quality is poor, do not execute
- **WAIT**: Signal has potential but timing is not optimal

### 6. CONFIDENCE SCORE
Rate your confidence in this analysis on a scale of 1-10, where:
- 1-3: Low confidence, high uncertainty
- 4-6: Moderate confidence, some concerns
- 7-8: High confidence, good signal
- 9-10: Very high confidence, excellent signal

Please provide specific reasoning for your recommendation and confidence score.

---
*This analysis will be used to determine whether to execute this trading signal. Please be thorough and objective in your assessment.*"""

    return prompt


def format_market_analysis_prompt(symbols: List[str], timeframe: str = "1h", 
                                context: str = "") -> str:
    """
    Format a market analysis request prompt.
    
    Args:
        symbols: List of trading symbols to analyze
        timeframe: Analysis timeframe
        context: Additional context or specific questions
        
    Returns:
        str: Formatted market analysis prompt
    """
    symbols_str = ", ".join(symbols)
    
    prompt = f"""# Market Analysis Request

## 📊 ANALYSIS SCOPE
**Symbols:** {symbols_str}
**Timeframe:** {timeframe}
**Analysis Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}

## 🎯 ANALYSIS REQUEST

Please provide a comprehensive market analysis covering:

### 1. OVERALL MARKET SENTIMENT
- Current market trend and momentum
- Key support and resistance levels
- Market volatility assessment

### 2. SYMBOL-SPECIFIC ANALYSIS
For each symbol ({symbols_str}):
- Technical outlook and key levels
- Recent price action and patterns
- Potential trading opportunities

### 3. RISK FACTORS
- Current market risks and concerns
- Potential catalysts (positive and negative)
- Recommended risk management approach

### 4. TRADING RECOMMENDATIONS
- Best symbols to focus on currently
- Suggested timeframes for analysis
- Overall market bias (bullish/bearish/neutral)

{f"### 5. SPECIFIC CONTEXT\\n{context}" if context else ""}

Please provide actionable insights for trading decisions.
"""
    
    return prompt


def validate_trade_data(trade: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate and clean trade data before formatting.
    
    Args:
        trade: Raw trade data dictionary
        
    Returns:
        dict: Validated and cleaned trade data
    """
    # Required fields with defaults
    validated_trade = {
        'action': str(trade.get('action', 'UNKNOWN')).upper(),
        'price': float(trade.get('price', 0.0)),
        'stop_loss': float(trade.get('stop_loss', 0.0)),
        'take_profit': float(trade.get('take_profit', 0.0)),
        'confidence': float(trade.get('confidence', 0.0)),
        'reason': str(trade.get('reason', 'No reason provided')),
        'metadata': trade.get('metadata', {})
    }
    
    # Validate action
    if validated_trade['action'] not in ['BUY', 'SELL']:
        validated_trade['action'] = 'UNKNOWN'
    
    # Ensure confidence is between 0 and 1
    if validated_trade['confidence'] > 1:
        validated_trade['confidence'] = validated_trade['confidence'] / 100
    
    # Ensure metadata has required fields
    metadata = validated_trade['metadata']
    if 'strategy_name' not in metadata:
        metadata['strategy_name'] = 'unknown_strategy'
    if 'symbol' not in metadata:
        metadata['symbol'] = 'UNKNOWN'
    if 'timeframe' not in metadata:
        metadata['timeframe'] = '1h'
    
    return validated_trade


# Example usage and testing
if __name__ == "__main__":
    # Test the prompt formatter
    print("Testing prompt formatter...")

    # Sample trade data
    sample_trade = {
        'action': 'BUY',
        'price': 50000.0,
        'stop_loss': 49000.0,
        'take_profit': 52000.0,
        'confidence': 0.85,
        'reason': 'Bullish RSI divergence detected with strong volume confirmation',
        'metadata': {
            'strategy_name': 'rsi_divergence',
            'symbol': 'BTCUSDT',
            'timeframe': '1h'
        }
    }

    print("\n1. Testing main format_prompt function...")
    prompt = format_prompt(sample_trade)
    print("✅ Prompt generated successfully")
    print(f"Prompt length: {len(prompt)} characters")

    print("\n2. Testing market analysis prompt...")
    market_prompt = format_market_analysis_prompt(['BTCUSDT', 'ETHUSDT'], '4h')
    print("✅ Market analysis prompt generated")

    print("\n3. Testing data validation...")
    invalid_trade = {'action': 'invalid', 'confidence': 150}
    validated = validate_trade_data(invalid_trade)
    print(f"✅ Validation fixed action: {validated['action']}")
    print(f"✅ Validation fixed confidence: {validated['confidence']}")

    print("\n✅ All prompt formatter tests completed!")
    print("The prompt_formatter.py module is ready for AI integration.")

    # Optionally print a sample prompt
    print("\n" + "="*60)
    print("SAMPLE PROMPT OUTPUT:")
    print("="*60)
    print(prompt[:500] + "..." if len(prompt) > 500 else prompt)
