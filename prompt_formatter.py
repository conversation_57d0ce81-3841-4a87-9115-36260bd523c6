"""
Prompt formatter module for generating clean, structured prompt messages for AI review.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
import json

from strategies.base_strategy import TradingSignal
from trade import TradeRecord


class PromptFormatter:
    """
    Formats trading data into structured prompts for AI analysis and review.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize prompt formatter.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        self.include_technical_details = self.config.get('include_technical_details', True)
        self.include_market_context = self.config.get('include_market_context', True)
        self.max_history_length = self.config.get('max_history_length', 10)
    
    def format_signal_analysis_prompt(self, signal: TradingSignal, 
                                    market_data: Dict[str, Any],
                                    trade_history: List[TradeRecord] = None) -> str:
        """
        Format a prompt for AI analysis of a trading signal.
        
        Args:
            signal: Trading signal to analyze
            market_data: Current market data
            trade_history: Recent trade history
            
        Returns:
            str: Formatted prompt for AI analysis
        """
        prompt_sections = []
        
        # Header
        prompt_sections.append("# Trading Signal Analysis Request")
        prompt_sections.append(f"**Analysis Time:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}")
        prompt_sections.append("")
        
        # Signal Details
        prompt_sections.append("## Signal Details")
        prompt_sections.append(f"- **Symbol:** {signal.symbol}")
        prompt_sections.append(f"- **Action:** {signal.action}")
        prompt_sections.append(f"- **Price:** ${signal.price:.4f}")
        prompt_sections.append(f"- **Strategy:** {signal.metadata.get('strategy', 'Unknown')}")
        prompt_sections.append(f"- **Pattern:** {signal.metadata.get('pattern', 'N/A')}")
        prompt_sections.append(f"- **Timeframe:** {signal.timeframe}")
        prompt_sections.append(f"- **Confidence:** {signal.confidence:.1%}")
        prompt_sections.append(f"- **Timestamp:** {signal.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if signal.stop_loss:
            prompt_sections.append(f"- **Stop Loss:** ${signal.stop_loss:.4f}")
        if signal.take_profit:
            prompt_sections.append(f"- **Take Profit:** ${signal.take_profit:.4f}")
        
        prompt_sections.append("")
        
        # Market Context
        if self.include_market_context and market_data:
            prompt_sections.append("## Market Context")
            prompt_sections.extend(self._format_market_context(signal.symbol, market_data))
            prompt_sections.append("")
        
        # Technical Analysis
        if self.include_technical_details and market_data:
            prompt_sections.append("## Technical Analysis")
            prompt_sections.extend(self._format_technical_analysis(signal, market_data))
            prompt_sections.append("")
        
        # Trade History Context
        if trade_history:
            prompt_sections.append("## Recent Trade History")
            prompt_sections.extend(self._format_trade_history(trade_history))
            prompt_sections.append("")
        
        # Analysis Request
        prompt_sections.append("## Analysis Request")
        prompt_sections.append("Please provide a comprehensive analysis of this trading signal including:")
        prompt_sections.append("1. **Signal Validity:** Assess the strength and reliability of the signal")
        prompt_sections.append("2. **Risk Assessment:** Evaluate potential risks and risk/reward ratio")
        prompt_sections.append("3. **Market Conditions:** Consider current market conditions and trends")
        prompt_sections.append("4. **Entry Strategy:** Recommend optimal entry timing and conditions")
        prompt_sections.append("5. **Exit Strategy:** Suggest stop loss and take profit adjustments if needed")
        prompt_sections.append("6. **Position Sizing:** Recommend appropriate position size based on risk")
        prompt_sections.append("7. **Overall Recommendation:** Provide a clear recommendation (EXECUTE, MODIFY, or REJECT)")
        prompt_sections.append("")
        prompt_sections.append("Please format your response with clear sections and actionable insights.")
        
        return "\n".join(prompt_sections)
    
    def format_trade_review_prompt(self, trade_record: TradeRecord, 
                                 market_data: Dict[str, Any] = None) -> str:
        """
        Format a prompt for AI review of a completed trade.
        
        Args:
            trade_record: Completed trade record
            market_data: Market data during the trade period
            
        Returns:
            str: Formatted prompt for trade review
        """
        prompt_sections = []
        
        # Header
        prompt_sections.append("# Trade Performance Review")
        prompt_sections.append(f"**Review Time:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}")
        prompt_sections.append("")
        
        # Trade Summary
        prompt_sections.append("## Trade Summary")
        prompt_sections.append(f"- **Trade ID:** {trade_record.trade_id}")
        prompt_sections.append(f"- **Symbol:** {trade_record.symbol}")
        prompt_sections.append(f"- **Strategy:** {trade_record.strategy}")
        prompt_sections.append(f"- **Action:** {trade_record.action}")
        prompt_sections.append(f"- **Entry Price:** ${trade_record.entry_price:.4f}")
        prompt_sections.append(f"- **Exit Price:** ${trade_record.exit_price:.4f}")
        prompt_sections.append(f"- **Quantity:** {trade_record.quantity:.6f}")
        prompt_sections.append(f"- **P&L:** ${trade_record.pnl:.2f} ({trade_record.pnl_percentage:.2f}%)")
        prompt_sections.append(f"- **Fees:** ${trade_record.fees:.2f}")
        prompt_sections.append(f"- **Duration:** {self._calculate_trade_duration(trade_record)}")
        prompt_sections.append(f"- **Status:** {trade_record.status}")
        prompt_sections.append("")
        
        # Risk Management
        prompt_sections.append("## Risk Management")
        if trade_record.stop_loss:
            prompt_sections.append(f"- **Stop Loss:** ${trade_record.stop_loss:.4f}")
        if trade_record.take_profit:
            prompt_sections.append(f"- **Take Profit:** ${trade_record.take_profit:.4f}")
        
        # Calculate risk/reward ratio
        if trade_record.stop_loss and trade_record.take_profit:
            risk = abs(trade_record.entry_price - trade_record.stop_loss)
            reward = abs(trade_record.take_profit - trade_record.entry_price)
            rr_ratio = reward / risk if risk > 0 else 0
            prompt_sections.append(f"- **Risk/Reward Ratio:** 1:{rr_ratio:.2f}")
        
        prompt_sections.append("")
        
        # Market Context During Trade
        if market_data:
            prompt_sections.append("## Market Context During Trade")
            prompt_sections.extend(self._format_market_context(trade_record.symbol, market_data))
            prompt_sections.append("")
        
        # Review Request
        prompt_sections.append("## Review Request")
        prompt_sections.append("Please provide a detailed review of this trade including:")
        prompt_sections.append("1. **Trade Execution:** Assess the quality of entry and exit timing")
        prompt_sections.append("2. **Strategy Performance:** Evaluate how well the strategy performed")
        prompt_sections.append("3. **Risk Management:** Review the effectiveness of stop loss and take profit")
        prompt_sections.append("4. **Market Conditions:** Analyze how market conditions affected the trade")
        prompt_sections.append("5. **Lessons Learned:** Identify key takeaways and improvements")
        prompt_sections.append("6. **Strategy Adjustments:** Suggest any strategy parameter adjustments")
        prompt_sections.append("7. **Future Recommendations:** Provide recommendations for similar setups")
        prompt_sections.append("")
        prompt_sections.append("Please provide specific, actionable insights for improving future trading performance.")
        
        return "\n".join(prompt_sections)
    
    def format_portfolio_analysis_prompt(self, trades: List[TradeRecord], 
                                       account_balance: float,
                                       time_period: str = "last 30 days") -> str:
        """
        Format a prompt for AI analysis of portfolio performance.
        
        Args:
            trades: List of trades to analyze
            account_balance: Current account balance
            time_period: Time period for analysis
            
        Returns:
            str: Formatted prompt for portfolio analysis
        """
        prompt_sections = []
        
        # Header
        prompt_sections.append("# Portfolio Performance Analysis")
        prompt_sections.append(f"**Analysis Period:** {time_period}")
        prompt_sections.append(f"**Analysis Time:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}")
        prompt_sections.append("")
        
        # Portfolio Summary
        prompt_sections.append("## Portfolio Summary")
        prompt_sections.append(f"- **Current Balance:** ${account_balance:.2f}")
        prompt_sections.append(f"- **Total Trades:** {len(trades)}")
        
        # Calculate portfolio metrics
        winning_trades = [t for t in trades if t.pnl > 0]
        losing_trades = [t for t in trades if t.pnl < 0]
        total_pnl = sum(t.pnl for t in trades)
        win_rate = (len(winning_trades) / len(trades)) * 100 if trades else 0
        
        prompt_sections.append(f"- **Winning Trades:** {len(winning_trades)}")
        prompt_sections.append(f"- **Losing Trades:** {len(losing_trades)}")
        prompt_sections.append(f"- **Win Rate:** {win_rate:.1f}%")
        prompt_sections.append(f"- **Total P&L:** ${total_pnl:.2f}")
        
        if winning_trades:
            avg_win = sum(t.pnl for t in winning_trades) / len(winning_trades)
            prompt_sections.append(f"- **Average Win:** ${avg_win:.2f}")
        
        if losing_trades:
            avg_loss = sum(t.pnl for t in losing_trades) / len(losing_trades)
            prompt_sections.append(f"- **Average Loss:** ${avg_loss:.2f}")
        
        prompt_sections.append("")
        
        # Strategy Breakdown
        prompt_sections.append("## Strategy Performance")
        strategy_performance = self._analyze_strategy_performance(trades)
        for strategy, metrics in strategy_performance.items():
            prompt_sections.append(f"### {strategy}")
            prompt_sections.append(f"- Trades: {metrics['count']}")
            prompt_sections.append(f"- Win Rate: {metrics['win_rate']:.1f}%")
            prompt_sections.append(f"- Total P&L: ${metrics['total_pnl']:.2f}")
            prompt_sections.append("")
        
        # Recent Trades
        prompt_sections.append("## Recent Trades")
        recent_trades = trades[-self.max_history_length:] if trades else []
        for trade in recent_trades:
            status_emoji = "✅" if trade.pnl > 0 else "❌"
            prompt_sections.append(f"- {status_emoji} {trade.symbol} {trade.action} | "
                                 f"P&L: ${trade.pnl:.2f} | Strategy: {trade.strategy}")
        prompt_sections.append("")
        
        # Analysis Request
        prompt_sections.append("## Analysis Request")
        prompt_sections.append("Please provide a comprehensive portfolio analysis including:")
        prompt_sections.append("1. **Overall Performance:** Assess the portfolio's overall performance")
        prompt_sections.append("2. **Strategy Effectiveness:** Evaluate which strategies are performing best/worst")
        prompt_sections.append("3. **Risk Management:** Review risk management effectiveness")
        prompt_sections.append("4. **Diversification:** Assess portfolio diversification and concentration risks")
        prompt_sections.append("5. **Improvement Areas:** Identify specific areas for improvement")
        prompt_sections.append("6. **Strategy Adjustments:** Recommend strategy parameter adjustments")
        prompt_sections.append("7. **Risk Adjustments:** Suggest risk management improvements")
        prompt_sections.append("8. **Action Plan:** Provide a clear action plan for optimization")
        
        return "\n".join(prompt_sections)
    
    def format_market_analysis_prompt(self, market_data: Dict[str, Any], 
                                    symbols: List[str] = None) -> str:
        """
        Format a prompt for AI analysis of current market conditions.
        
        Args:
            market_data: Current market data
            symbols: List of symbols to analyze
            
        Returns:
            str: Formatted prompt for market analysis
        """
        prompt_sections = []
        
        # Header
        prompt_sections.append("# Market Conditions Analysis")
        prompt_sections.append(f"**Analysis Time:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}")
        prompt_sections.append("")
        
        # Market Overview
        if symbols:
            prompt_sections.append("## Market Overview")
            for symbol in symbols:
                if symbol in market_data:
                    symbol_data = market_data[symbol]
                    prompt_sections.extend(self._format_symbol_overview(symbol, symbol_data))
            prompt_sections.append("")
        
        # Technical Analysis Summary
        prompt_sections.append("## Technical Analysis Summary")
        prompt_sections.extend(self._format_technical_summary(market_data))
        prompt_sections.append("")
        
        # Analysis Request
        prompt_sections.append("## Analysis Request")
        prompt_sections.append("Please provide a comprehensive market analysis including:")
        prompt_sections.append("1. **Market Trend:** Identify the overall market trend and direction")
        prompt_sections.append("2. **Key Levels:** Identify important support and resistance levels")
        prompt_sections.append("3. **Volatility Assessment:** Assess current market volatility")
        prompt_sections.append("4. **Trading Opportunities:** Identify potential trading opportunities")
        prompt_sections.append("5. **Risk Factors:** Highlight current market risks and concerns")
        prompt_sections.append("6. **Strategy Recommendations:** Suggest which strategies might work best in current conditions")
        prompt_sections.append("7. **Timeframe Analysis:** Provide insights across different timeframes")
        
        return "\n".join(prompt_sections)
    
    def _format_market_context(self, symbol: str, market_data: Dict[str, Any]) -> List[str]:
        """Format market context information."""
        context_lines = []
        
        if symbol in market_data:
            symbol_data = market_data[symbol]
            
            for timeframe in ['1h', '4h', '1d']:
                if timeframe in symbol_data:
                    tf_data = symbol_data[timeframe]
                    current_price = tf_data['close'][-1] if tf_data['close'] else 0
                    
                    context_lines.append(f"### {timeframe} Timeframe")
                    context_lines.append(f"- **Current Price:** ${current_price:.4f}")
                    
                    if 'rsi' in tf_data and tf_data['rsi']:
                        rsi = tf_data['rsi'][-1]
                        context_lines.append(f"- **RSI:** {rsi:.2f}")
                    
                    if 'sma_20' in tf_data and tf_data['sma_20']:
                        sma20 = tf_data['sma_20'][-1]
                        context_lines.append(f"- **SMA 20:** ${sma20:.4f}")
                    
                    context_lines.append("")
        
        return context_lines
    
    def _format_technical_analysis(self, signal: TradingSignal, market_data: Dict[str, Any]) -> List[str]:
        """Format technical analysis information."""
        analysis_lines = []
        
        # Add signal-specific technical details
        if signal.metadata:
            for key, value in signal.metadata.items():
                if key not in ['strategy', 'pattern']:
                    analysis_lines.append(f"- **{key.replace('_', ' ').title()}:** {value}")
        
        # Add relevant indicators from market data
        symbol_data = market_data.get(signal.symbol, {})
        tf_data = symbol_data.get(signal.timeframe, {})
        
        if tf_data:
            indicators = ['rsi', 'macd', 'bb_upper', 'bb_lower', 'atr']
            for indicator in indicators:
                if indicator in tf_data and tf_data[indicator]:
                    value = tf_data[indicator][-1]
                    analysis_lines.append(f"- **{indicator.upper()}:** {value:.4f}")
        
        return analysis_lines
    
    def _format_trade_history(self, trades: List[TradeRecord]) -> List[str]:
        """Format recent trade history."""
        history_lines = []
        
        recent_trades = trades[-self.max_history_length:] if trades else []
        
        for trade in recent_trades:
            status_emoji = "✅" if trade.pnl > 0 else "❌"
            duration = self._calculate_trade_duration(trade)
            
            history_lines.append(
                f"- {status_emoji} **{trade.symbol}** {trade.action} | "
                f"Entry: ${trade.entry_price:.4f} | "
                f"Exit: ${trade.exit_price:.4f} | "
                f"P&L: ${trade.pnl:.2f} ({trade.pnl_percentage:.1f}%) | "
                f"Duration: {duration} | "
                f"Strategy: {trade.strategy}"
            )
        
        return history_lines
    
    def _calculate_trade_duration(self, trade: TradeRecord) -> str:
        """Calculate and format trade duration."""
        if trade.exit_time and trade.entry_time:
            duration = trade.exit_time - trade.entry_time
            hours = duration.total_seconds() / 3600
            
            if hours < 1:
                return f"{int(duration.total_seconds() / 60)}m"
            elif hours < 24:
                return f"{hours:.1f}h"
            else:
                days = hours / 24
                return f"{days:.1f}d"
        
        return "N/A"
    
    def _analyze_strategy_performance(self, trades: List[TradeRecord]) -> Dict[str, Dict[str, Any]]:
        """Analyze performance by strategy."""
        strategy_stats = {}
        
        for trade in trades:
            strategy = trade.strategy
            if strategy not in strategy_stats:
                strategy_stats[strategy] = {
                    'trades': [],
                    'count': 0,
                    'wins': 0,
                    'total_pnl': 0.0
                }
            
            strategy_stats[strategy]['trades'].append(trade)
            strategy_stats[strategy]['count'] += 1
            strategy_stats[strategy]['total_pnl'] += trade.pnl
            
            if trade.pnl > 0:
                strategy_stats[strategy]['wins'] += 1
        
        # Calculate win rates
        for strategy, stats in strategy_stats.items():
            stats['win_rate'] = (stats['wins'] / stats['count']) * 100 if stats['count'] > 0 else 0
        
        return strategy_stats
    
    def _format_symbol_overview(self, symbol: str, symbol_data: Dict[str, Any]) -> List[str]:
        """Format symbol overview information."""
        overview_lines = []
        overview_lines.append(f"### {symbol}")
        
        # Get data from multiple timeframes
        for timeframe in ['1h', '4h', '1d']:
            if timeframe in symbol_data:
                tf_data = symbol_data[timeframe]
                if tf_data.get('close'):
                    price = tf_data['close'][-1]
                    overview_lines.append(f"- **{timeframe} Price:** ${price:.4f}")
        
        overview_lines.append("")
        return overview_lines
    
    def _format_technical_summary(self, market_data: Dict[str, Any]) -> List[str]:
        """Format technical analysis summary."""
        summary_lines = []
        
        # TODO: Implement comprehensive technical summary
        # This would analyze indicators across all symbols and timeframes
        # and provide a market-wide technical overview
        
        summary_lines.append("- Market technical analysis summary would go here")
        summary_lines.append("- This would include trend analysis, volatility assessment, etc.")
        
        return summary_lines
