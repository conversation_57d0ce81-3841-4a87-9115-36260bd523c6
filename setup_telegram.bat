@echo off
echo Setting up Telegram configuration for Trading Bot...
echo.

echo To get your Telegram Bot Token:
echo 1. Message @BotFather on Telegram
echo 2. Send /newbot
echo 3. Follow the instructions to create a bot
echo 4. Copy the token (looks like: 123456789:ABCdefGHIjklMNOpqrsTUVwxyz)
echo.

echo To get your Chat ID:
echo 1. Message your bot first (send any message)
echo 2. Visit: https://api.telegram.org/bot[YOUR_BOT_TOKEN]/getUpdates
echo 3. Look for "chat":{"id": YOUR_CHAT_ID
echo 4. Copy the chat ID (looks like: 123456789 or -123456789)
echo.

set /p BOT_TOKEN="Enter your Telegram Bot Token: "
set /p CHAT_ID="Enter your Telegram Chat ID: "

echo.
echo Setting environment variables...

setx TELEGRAM_BOT_TOKEN "%BOT_TOKEN%"
setx TELEGRAM_CHAT_ID "%CHAT_ID%"

echo.
echo ✅ Environment variables set!
echo ⚠️  Please restart your command prompt or IDE for changes to take effect.
echo.
echo You can now run the bot and it will send Telegram notifications.
echo.
pause
