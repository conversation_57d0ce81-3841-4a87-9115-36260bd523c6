"""
Performance metrics calculation for backtesting results.
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import math

from trade import TradeRecord


class PerformanceMetrics:
    """
    Calculates various performance metrics for backtest results.
    """
    
    @staticmethod
    def calculate_total_return(initial_balance: float, final_balance: float) -> float:
        """
        Calculate total return percentage.
        
        Args:
            initial_balance: Starting balance
            final_balance: Ending balance
            
        Returns:
            float: Total return as percentage
        """
        if initial_balance == 0:
            return 0.0
        
        return ((final_balance - initial_balance) / initial_balance) * 100
    
    @staticmethod
    def calculate_annualized_return(total_return: float, days: int) -> float:
        """
        Calculate annualized return.
        
        Args:
            total_return: Total return as percentage
            days: Number of days in the period
            
        Returns:
            float: Annualized return as percentage
        """
        if days <= 0:
            return 0.0
        
        years = days / 365.25
        return ((1 + total_return / 100) ** (1 / years) - 1) * 100
    
    @staticmethod
    def calculate_sharpe_ratio(returns: List[float], risk_free_rate: float = 0.02) -> float:
        """
        Calculate Sharpe ratio.
        
        Args:
            returns: List of period returns
            risk_free_rate: Risk-free rate (annual)
            
        Returns:
            float: Sharpe ratio
        """
        if not returns or len(returns) < 2:
            return 0.0
        
        returns_array = np.array(returns)
        excess_returns = returns_array - (risk_free_rate / 252)  # Daily risk-free rate
        
        if np.std(excess_returns) == 0:
            return 0.0
        
        return np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
    
    @staticmethod
    def calculate_sortino_ratio(returns: List[float], risk_free_rate: float = 0.02) -> float:
        """
        Calculate Sortino ratio (downside deviation).
        
        Args:
            returns: List of period returns
            risk_free_rate: Risk-free rate (annual)
            
        Returns:
            float: Sortino ratio
        """
        if not returns or len(returns) < 2:
            return 0.0
        
        returns_array = np.array(returns)
        excess_returns = returns_array - (risk_free_rate / 252)
        
        # Calculate downside deviation
        negative_returns = excess_returns[excess_returns < 0]
        if len(negative_returns) == 0:
            return float('inf')
        
        downside_deviation = np.sqrt(np.mean(negative_returns ** 2))
        
        if downside_deviation == 0:
            return 0.0
        
        return np.mean(excess_returns) / downside_deviation * np.sqrt(252)
    
    @staticmethod
    def calculate_max_drawdown(equity_curve: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate maximum drawdown.
        
        Args:
            equity_curve: List of equity values over time
            
        Returns:
            dict: Max drawdown information
        """
        if not equity_curve:
            return {'max_drawdown': 0.0, 'max_drawdown_duration': 0, 'peak': 0, 'trough': 0}
        
        equity_values = [point['equity'] for point in equity_curve]
        
        peak = equity_values[0]
        max_drawdown = 0.0
        max_drawdown_duration = 0
        current_drawdown_duration = 0
        peak_index = 0
        trough_index = 0
        max_dd_peak = 0
        max_dd_trough = 0
        
        for i, equity in enumerate(equity_values):
            if equity > peak:
                peak = equity
                peak_index = i
                current_drawdown_duration = 0
            else:
                current_drawdown_duration += 1
                drawdown = (peak - equity) / peak
                
                if drawdown > max_drawdown:
                    max_drawdown = drawdown
                    max_drawdown_duration = current_drawdown_duration
                    trough_index = i
                    max_dd_peak = peak
                    max_dd_trough = equity
        
        return {
            'max_drawdown': max_drawdown * 100,  # As percentage
            'max_drawdown_duration': max_drawdown_duration,
            'peak': max_dd_peak,
            'trough': max_dd_trough,
            'peak_date': equity_curve[peak_index]['timestamp'] if peak_index < len(equity_curve) else None,
            'trough_date': equity_curve[trough_index]['timestamp'] if trough_index < len(equity_curve) else None
        }
    
    @staticmethod
    def calculate_calmar_ratio(annualized_return: float, max_drawdown: float) -> float:
        """
        Calculate Calmar ratio.
        
        Args:
            annualized_return: Annualized return as percentage
            max_drawdown: Maximum drawdown as percentage
            
        Returns:
            float: Calmar ratio
        """
        if max_drawdown == 0:
            return float('inf') if annualized_return > 0 else 0.0
        
        return annualized_return / max_drawdown
    
    @staticmethod
    def calculate_win_rate(trades: List[TradeRecord]) -> float:
        """
        Calculate win rate.
        
        Args:
            trades: List of completed trades
            
        Returns:
            float: Win rate as percentage
        """
        if not trades:
            return 0.0
        
        winning_trades = len([trade for trade in trades if trade.pnl > 0])
        return (winning_trades / len(trades)) * 100
    
    @staticmethod
    def calculate_profit_factor(trades: List[TradeRecord]) -> float:
        """
        Calculate profit factor.
        
        Args:
            trades: List of completed trades
            
        Returns:
            float: Profit factor
        """
        if not trades:
            return 0.0
        
        gross_profit = sum(trade.pnl for trade in trades if trade.pnl > 0)
        gross_loss = abs(sum(trade.pnl for trade in trades if trade.pnl < 0))
        
        if gross_loss == 0:
            return float('inf') if gross_profit > 0 else 0.0
        
        return gross_profit / gross_loss
    
    @staticmethod
    def calculate_average_trade_duration(trades: List[TradeRecord]) -> float:
        """
        Calculate average trade duration in hours.
        
        Args:
            trades: List of completed trades
            
        Returns:
            float: Average duration in hours
        """
        if not trades:
            return 0.0
        
        durations = []
        for trade in trades:
            if trade.entry_time and trade.exit_time:
                duration = (trade.exit_time - trade.entry_time).total_seconds() / 3600
                durations.append(duration)
        
        return np.mean(durations) if durations else 0.0
    
    @staticmethod
    def calculate_expectancy(trades: List[TradeRecord]) -> float:
        """
        Calculate expectancy (average trade result).
        
        Args:
            trades: List of completed trades
            
        Returns:
            float: Expectancy
        """
        if not trades:
            return 0.0
        
        win_rate = PerformanceMetrics.calculate_win_rate(trades) / 100
        
        winning_trades = [trade.pnl for trade in trades if trade.pnl > 0]
        losing_trades = [trade.pnl for trade in trades if trade.pnl < 0]
        
        avg_win = np.mean(winning_trades) if winning_trades else 0
        avg_loss = np.mean(losing_trades) if losing_trades else 0
        
        return (win_rate * avg_win) + ((1 - win_rate) * avg_loss)
    
    @staticmethod
    def calculate_kelly_criterion(trades: List[TradeRecord]) -> float:
        """
        Calculate Kelly criterion for optimal position sizing.
        
        Args:
            trades: List of completed trades
            
        Returns:
            float: Kelly percentage
        """
        if not trades:
            return 0.0
        
        win_rate = PerformanceMetrics.calculate_win_rate(trades) / 100
        
        winning_trades = [trade.pnl for trade in trades if trade.pnl > 0]
        losing_trades = [abs(trade.pnl) for trade in trades if trade.pnl < 0]
        
        if not winning_trades or not losing_trades:
            return 0.0
        
        avg_win = np.mean(winning_trades)
        avg_loss = np.mean(losing_trades)
        
        if avg_loss == 0:
            return 0.0
        
        win_loss_ratio = avg_win / avg_loss
        kelly = win_rate - ((1 - win_rate) / win_loss_ratio)
        
        return max(0, min(kelly, 0.25)) * 100  # Cap at 25%
    
    @staticmethod
    def calculate_var(returns: List[float], confidence_level: float = 0.05) -> float:
        """
        Calculate Value at Risk (VaR).
        
        Args:
            returns: List of period returns
            confidence_level: Confidence level (e.g., 0.05 for 95% VaR)
            
        Returns:
            float: VaR value
        """
        if not returns:
            return 0.0
        
        return np.percentile(returns, confidence_level * 100)
    
    @staticmethod
    def calculate_cvar(returns: List[float], confidence_level: float = 0.05) -> float:
        """
        Calculate Conditional Value at Risk (CVaR).
        
        Args:
            returns: List of period returns
            confidence_level: Confidence level
            
        Returns:
            float: CVaR value
        """
        if not returns:
            return 0.0
        
        var = PerformanceMetrics.calculate_var(returns, confidence_level)
        tail_returns = [r for r in returns if r <= var]
        
        return np.mean(tail_returns) if tail_returns else 0.0


class BacktestAnalyzer:
    """
    Comprehensive analyzer for backtest results.
    """
    
    def __init__(self):
        """Initialize backtest analyzer."""
        self.metrics = PerformanceMetrics()
    
    def analyze_backtest(self, backtest_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform comprehensive analysis of backtest results.
        
        Args:
            backtest_results: Backtest results dictionary
            
        Returns:
            dict: Comprehensive analysis results
        """
        trades = backtest_results.get('trades', [])
        equity_curve = backtest_results.get('equity_curve', [])
        initial_balance = backtest_results.get('initial_balance', 0)
        final_balance = backtest_results.get('final_balance', 0)
        
        # Calculate returns
        returns = self._calculate_returns(equity_curve)
        
        # Calculate all metrics
        analysis = {
            'basic_metrics': self._calculate_basic_metrics(backtest_results),
            'risk_metrics': self._calculate_risk_metrics(returns, equity_curve),
            'trade_metrics': self._calculate_trade_metrics(trades),
            'advanced_metrics': self._calculate_advanced_metrics(trades, returns),
            'monthly_returns': self._calculate_monthly_returns(equity_curve),
            'drawdown_analysis': self._analyze_drawdowns(equity_curve)
        }
        
        return analysis
    
    def _calculate_returns(self, equity_curve: List[Dict[str, Any]]) -> List[float]:
        """Calculate period returns from equity curve."""
        if len(equity_curve) < 2:
            return []
        
        returns = []
        for i in range(1, len(equity_curve)):
            prev_equity = equity_curve[i - 1]['equity']
            curr_equity = equity_curve[i]['equity']
            
            if prev_equity != 0:
                ret = (curr_equity - prev_equity) / prev_equity
                returns.append(ret)
        
        return returns
    
    def _calculate_basic_metrics(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate basic performance metrics."""
        initial_balance = results.get('initial_balance', 0)
        final_balance = results.get('final_balance', 0)
        
        total_return = self.metrics.calculate_total_return(initial_balance, final_balance)
        
        # Calculate duration
        start_date = results.get('start_date')
        end_date = results.get('end_date')
        days = (end_date - start_date).days if start_date and end_date else 0
        
        annualized_return = self.metrics.calculate_annualized_return(total_return, days)
        
        return {
            'initial_balance': initial_balance,
            'final_balance': final_balance,
            'total_return': total_return,
            'annualized_return': annualized_return,
            'duration_days': days
        }
    
    def _calculate_risk_metrics(self, returns: List[float], 
                              equity_curve: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate risk-related metrics."""
        sharpe_ratio = self.metrics.calculate_sharpe_ratio(returns)
        sortino_ratio = self.metrics.calculate_sortino_ratio(returns)
        max_dd_info = self.metrics.calculate_max_drawdown(equity_curve)
        var_95 = self.metrics.calculate_var(returns, 0.05)
        cvar_95 = self.metrics.calculate_cvar(returns, 0.05)
        
        return {
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'max_drawdown': max_dd_info,
            'volatility': np.std(returns) * np.sqrt(252) * 100 if returns else 0,
            'var_95': var_95 * 100,
            'cvar_95': cvar_95 * 100
        }
    
    def _calculate_trade_metrics(self, trades: List[TradeRecord]) -> Dict[str, Any]:
        """Calculate trade-related metrics."""
        return {
            'total_trades': len(trades),
            'win_rate': self.metrics.calculate_win_rate(trades),
            'profit_factor': self.metrics.calculate_profit_factor(trades),
            'avg_trade_duration': self.metrics.calculate_average_trade_duration(trades),
            'expectancy': self.metrics.calculate_expectancy(trades)
        }
    
    def _calculate_advanced_metrics(self, trades: List[TradeRecord], 
                                  returns: List[float]) -> Dict[str, Any]:
        """Calculate advanced metrics."""
        kelly_criterion = self.metrics.calculate_kelly_criterion(trades)
        
        return {
            'kelly_criterion': kelly_criterion,
            'information_ratio': 0.0,  # TODO: Implement
            'treynor_ratio': 0.0,  # TODO: Implement
            'jensen_alpha': 0.0  # TODO: Implement
        }
    
    def _calculate_monthly_returns(self, equity_curve: List[Dict[str, Any]]) -> Dict[str, float]:
        """Calculate monthly returns."""
        # TODO: Implement monthly returns calculation
        return {}
    
    def _analyze_drawdowns(self, equity_curve: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze drawdown patterns."""
        # TODO: Implement detailed drawdown analysis
        return {}
