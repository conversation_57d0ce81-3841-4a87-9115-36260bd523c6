"""
Configuration file for the trading bot.

Contains all bot settings, API keys, and trading parameters.
Edit this file to customize your bot's behavior.
"""

import os
from typing import Dict, Any, List

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Environment variables loaded from .env file")
except ImportError:
    print("⚠️ Warning: python-dotenv not installed. Install with: pip install python-dotenv")
except Exception as e:
    print(f"⚠️ Warning: Could not load .env file: {e}")


def get_config() -> Dict[str, Any]:
    """
    Get the complete bot configuration.
    
    Returns:
        dict: Complete configuration dictionary
    """
    return {
        # =============================================================================
        # TRADING CONFIGURATION
        # =============================================================================
        
        # Symbols to trade (add/remove as needed)
        'symbols': [
            'BTCUSDT',
            'ETHUSDT',
            'ADAUSDT',
            'DOTUSDT',
            'LINKUSDT'
        ],
        
        # Timeframes to analyze
        'timeframes': [
            '1h',
            '4h'
        ],
        
        # Bot execution settings
        'loop_interval': 300,  # Seconds between cycles (300 = 5 minutes)
        'send_cycle_summary': True,  # Send summary after each cycle
        'send_charts': True,  # Include charts in notifications
        
        # =============================================================================
        # EXCHANGE CONFIGURATION (Currently simulated)
        # =============================================================================
        
        'exchange': 'binance',  # Exchange name (for future integration)
        'api_key': os.getenv('EXCHANGE_API_KEY', 'your_api_key_here'),
        'api_secret': os.getenv('EXCHANGE_API_SECRET', 'your_api_secret_here'),
        'testnet': True,  # Use testnet for testing
        
        # =============================================================================
        # TELEGRAM NOTIFICATION CONFIGURATION
        # =============================================================================

        'telegram_enabled': os.getenv('TELEGRAM_ENABLED', 'false').lower() == 'true',  # Enable/disable Telegram
        'telegram_token': os.getenv('TELEGRAM_BOT_TOKEN', 'your_telegram_bot_token'),
        'telegram_chat_id': os.getenv('TELEGRAM_CHAT_ID', 'your_telegram_chat_id'),
        'send_charts': True,  # Include charts in notifications
        
        # Notification settings
        'send_startup_message': True,
        'send_shutdown_message': True,
        'send_error_notifications': True,
        'max_notifications_per_hour': 20,  # Rate limiting
        
        # =============================================================================
        # RISK MANAGEMENT CONFIGURATION
        # =============================================================================
        
        'risk_management': {
            'max_risk_per_trade': 0.02,  # 2% of account per trade
            'max_position_size': 0.1,    # 10% of account per position
            'max_open_positions': 5,     # Maximum concurrent positions
            'use_atr_stops': True,       # Use ATR for stop losses
            'atr_multiplier': 2.0,       # ATR multiplier for stops
            'risk_reward_ratio': 2.0,    # Minimum risk/reward ratio
        },
        
        # =============================================================================
        # MARKET DATA CONFIGURATION
        # =============================================================================
        
        'market_data': {
            'cache_duration': 60,        # Cache duration in seconds
            'max_candles': 200,          # Maximum candles to fetch
            'include_volume': True,      # Include volume data
            'calculate_indicators': True, # Calculate technical indicators
        },
        
        # =============================================================================
        # AI INTEGRATION CONFIGURATION (Optional)
        # =============================================================================
        
        'ai_integration': {
            'enabled': False,  # Enable AI signal confirmation
            'openai_api_key': os.getenv('OPENAI_API_KEY', ''),
            'model': 'gpt-4',
            'confidence_threshold': 0.7,  # Minimum AI confidence for execution
            'use_ai_for_analysis': True,
            'use_ai_for_confirmation': False,
        },
        
        # =============================================================================
        # N8N WORKFLOW INTEGRATION (Optional)
        # =============================================================================

        'n8n_integration': {
            'enabled': False,  # Set to True to enable N8N integration
            'base_url': os.getenv('N8N_BASE_URL', 'http://localhost:5678'),
            'api_key': os.getenv('N8N_API_KEY', ''),
            'webhook_url': os.getenv('N8N_WEBHOOK_URL', 'https://your-n8n-instance.com/webhook/trading-signal'),
            'workflows': {
                'signal_analysis': os.getenv('N8N_SIGNAL_WORKFLOW_ID', 'trading-signal'),
                'trade_notification': os.getenv('N8N_TRADE_WORKFLOW_ID', 'trade-notification'),
                'portfolio_analysis': os.getenv('N8N_PORTFOLIO_WORKFLOW_ID', 'portfolio-analysis'),
            },
            'timeout': 30,  # Request timeout in seconds
            'retry_attempts': 3,  # Number of retry attempts on failure
        },
        
        # =============================================================================
        # DATABASE CONFIGURATION (Optional)
        # =============================================================================
        
        'database': {
            'enabled': False,
            'url': os.getenv('DATABASE_URL', 'sqlite:///trading_bot.db'),
            'log_trades': True,
            'log_signals': True,
            'log_market_data': False,
        },
        
        # =============================================================================
        # BACKTESTING CONFIGURATION (Optional)
        # =============================================================================
        
        'backtesting': {
            'enabled': False,
            'initial_balance': 10000,
            'commission': 0.001,  # 0.1%
            'slippage': 0.0005,   # 0.05%
            'start_date': '2023-01-01',
            'end_date': '2023-12-31',
        },
        
        # =============================================================================
        # LOGGING CONFIGURATION
        # =============================================================================
        
        'log_level': 'INFO',  # DEBUG, INFO, WARNING, ERROR
        'log_to_file': True,
        'log_file': 'trading_bot.log',
        'max_log_size': 10 * 1024 * 1024,  # 10MB
        'log_backup_count': 5,
        
        # =============================================================================
        # DEVELOPMENT CONFIGURATION
        # =============================================================================
        
        'development': {
            'simulate_trades': True,     # Simulate trades instead of real execution
            'paper_trading': True,       # Use paper trading mode
            'debug_strategies': False,   # Enable strategy debugging
            'save_market_data': False,   # Save market data for analysis
            'test_notifications': False, # Send test notifications on startup
        }
    }


def get_trading_symbols() -> List[str]:
    """
    Get the list of trading symbols.
    
    Returns:
        list: List of trading symbols
    """
    config = get_config()
    return config['symbols']


def get_timeframes() -> List[str]:
    """
    Get the list of timeframes to analyze.
    
    Returns:
        list: List of timeframes
    """
    config = get_config()
    return config['timeframes']


def get_telegram_config() -> Dict[str, Any]:
    """
    Get Telegram configuration.

    Returns:
        dict: Telegram configuration with required keys
    """
    config = get_config()

    # Ensure all required keys are present with defaults
    telegram_config = {
        'telegram_enabled': config.get('telegram_enabled', True),
        'telegram_token': config.get('telegram_token', 'your_telegram_bot_token'),
        'telegram_chat_id': config.get('telegram_chat_id', 'your_telegram_chat_id'),
        'send_charts': config.get('send_charts', True)
    }

    # Only show warnings if Telegram is enabled
    if telegram_config['telegram_enabled']:
        if not telegram_config['telegram_token'] or telegram_config['telegram_token'] == 'your_telegram_bot_token':
            print("⚠️ Warning: Telegram bot token not configured. Set TELEGRAM_BOT_TOKEN environment variable.")

        if not telegram_config['telegram_chat_id'] or telegram_config['telegram_chat_id'] == 'your_telegram_chat_id':
            print("⚠️ Warning: Telegram chat ID not configured. Set TELEGRAM_CHAT_ID environment variable.")

    return telegram_config



def get_risk_config() -> Dict[str, Any]:
    """
    Get risk management configuration.

    Returns:
        dict: Risk management configuration
    """
    config = get_config()
    return config['risk_management']


def get_n8n_config() -> Dict[str, Any]:
    """
    Get N8N integration configuration.

    Returns:
        dict: N8N configuration including webhook URL and auth token
    """
    config = get_config()
    n8n_config = config['n8n_integration']

    # Use direct webhook URL if provided, otherwise build from base URL and workflow ID
    webhook_url = n8n_config.get('webhook_url')
    if not webhook_url and n8n_config['enabled'] and n8n_config['workflows']['signal_analysis']:
        base_url = n8n_config['base_url'].rstrip('/')
        workflow_id = n8n_config['workflows']['signal_analysis']
        webhook_url = f"{base_url}/webhook/{workflow_id}"

    return {
        'enabled': n8n_config['enabled'],
        'webhook_url': webhook_url,
        'n8n_webhook_url': webhook_url,  # Alternative key name
        'auth_token': n8n_config['api_key'],
        'api_key': n8n_config['api_key'],  # Alternative key name
        'base_url': n8n_config['base_url'],
        'workflows': n8n_config['workflows'],
        'timeout': n8n_config.get('timeout', 30),
        'retry_attempts': n8n_config.get('retry_attempts', 3)
    }


def validate_config() -> bool:
    """
    Validate the configuration.
    
    Returns:
        bool: True if configuration is valid
    """
    config = get_config()
    
    # Check required fields
    required_fields = ['symbols', 'timeframes']
    for field in required_fields:
        if not config.get(field):
            print(f"❌ Missing required config field: {field}")
            return False
    
    # Check symbols
    if not isinstance(config['symbols'], list) or len(config['symbols']) == 0:
        print("❌ Symbols must be a non-empty list")
        return False
    
    # Check timeframes
    valid_timeframes = ['1m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w']
    for tf in config['timeframes']:
        if tf not in valid_timeframes:
            print(f"❌ Invalid timeframe: {tf}")
            return False
    
    # Check loop interval
    if config['loop_interval'] < 60:
        print("⚠️ Warning: Loop interval less than 60 seconds may cause rate limiting")
    
    print("✅ Configuration validation passed")
    return True


def print_config_summary():
    """Print a summary of the current configuration."""
    config = get_config()
    
    print("\n" + "="*60)
    print("📋 TRADING BOT CONFIGURATION SUMMARY")
    print("="*60)
    
    print(f"🎯 Symbols: {', '.join(config['symbols'])}")
    print(f"⏰ Timeframes: {', '.join(config['timeframes'])}")
    print(f"🔄 Loop Interval: {config['loop_interval']} seconds")
    print(f"📊 Exchange: {config['exchange']} (Testnet: {config['testnet']})")
    print(f"📱 Telegram: {'✅ Configured' if config['telegram_token'] != 'your_telegram_bot_token' else '❌ Not configured'}")
    print(f"🤖 AI Integration: {'✅ Enabled' if config['ai_integration']['enabled'] else '❌ Disabled'}")
    print(f"🔗 N8N Integration: {'✅ Enabled' if config['n8n_integration']['enabled'] else '❌ Disabled'}")
    print(f"💾 Database: {'✅ Enabled' if config['database']['enabled'] else '❌ Disabled'}")
    print(f"📝 Log Level: {config['log_level']}")
    print(f"🧪 Development Mode: {'✅ Enabled' if config['development']['simulate_trades'] else '❌ Disabled'}")
    
    print("\n💰 Risk Management:")
    risk = config['risk_management']
    print(f"   • Max Risk per Trade: {risk['max_risk_per_trade']*100}%")
    print(f"   • Max Position Size: {risk['max_position_size']*100}%")
    print(f"   • Max Open Positions: {risk['max_open_positions']}")
    print(f"   • Risk/Reward Ratio: 1:{risk['risk_reward_ratio']}")
    
    print("="*60)


if __name__ == "__main__":
    # Validate and print configuration when run directly
    if validate_config():
        print_config_summary()
    else:
        print("❌ Configuration validation failed!")
