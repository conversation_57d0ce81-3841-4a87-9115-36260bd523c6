"""
Base strategy class that all trading strategies should inherit from.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime


@dataclass
class TradingSignal:
    """
    Represents a trading signal generated by a strategy.
    """
    symbol: str
    action: str  # 'BUY', 'SELL', 'CLOSE'
    price: float
    quantity: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    confidence: float = 0.0  # 0.0 to 1.0
    timeframe: str = '1h'
    timestamp: datetime = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.metadata is None:
            self.metadata = {}


class BaseStrategy(ABC):
    """
    Abstract base class for all trading strategies.
    """
    
    def __init__(self, params: Dict[str, Any]):
        """
        Initialize the strategy with parameters.
        
        Args:
            params: Strategy-specific parameters
        """
        self.params = params
        self.name = self.__class__.__name__
        self.is_active = True
        self.last_signal_time = None
        self.performance_metrics = {
            'total_signals': 0,
            'profitable_signals': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0
        }
    
    @abstractmethod
    def analyze(self, market_data: Dict[str, Any]) -> List[TradingSignal]:
        """
        Analyze market data and generate trading signals.
        
        Args:
            market_data: Dictionary containing market data (OHLCV, indicators, etc.)
            
        Returns:
            list: List of TradingSignal objects
        """
        pass
    
    @abstractmethod
    def validate_signal(self, signal: TradingSignal, market_data: Dict[str, Any]) -> bool:
        """
        Validate a trading signal before execution.
        
        Args:
            signal: The trading signal to validate
            market_data: Current market data
            
        Returns:
            bool: True if signal is valid, False otherwise
        """
        pass
    
    def get_required_indicators(self) -> List[str]:
        """
        Get list of required technical indicators for this strategy.
        
        Returns:
            list: List of indicator names
        """
        return []
    
    def get_required_timeframes(self) -> List[str]:
        """
        Get list of required timeframes for this strategy.
        
        Returns:
            list: List of timeframe strings (e.g., ['1m', '5m', '1h'])
        """
        return self.params.get('timeframes', ['1h'])
    
    def update_performance(self, signal: TradingSignal, pnl: float):
        """
        Update strategy performance metrics.
        
        Args:
            signal: The executed trading signal
            pnl: Profit/loss from the trade
        """
        self.performance_metrics['total_signals'] += 1
        self.performance_metrics['total_pnl'] += pnl
        
        if pnl > 0:
            self.performance_metrics['profitable_signals'] += 1
        
        # Calculate win rate
        if self.performance_metrics['total_signals'] > 0:
            self.performance_metrics['win_rate'] = (
                self.performance_metrics['profitable_signals'] / 
                self.performance_metrics['total_signals']
            )
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """
        Get strategy performance summary.
        
        Returns:
            dict: Performance metrics
        """
        return self.performance_metrics.copy()
    
    def reset_performance(self):
        """Reset performance metrics."""
        self.performance_metrics = {
            'total_signals': 0,
            'profitable_signals': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0
        }
    
    def set_active(self, active: bool):
        """
        Set strategy active status.
        
        Args:
            active: True to activate, False to deactivate
        """
        self.is_active = active
    
    def __str__(self) -> str:
        """String representation of the strategy."""
        return f"{self.name}(active={self.is_active}, signals={self.performance_metrics['total_signals']})"
