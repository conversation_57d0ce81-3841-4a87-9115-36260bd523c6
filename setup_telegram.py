"""
Telegram Setup Helper

This script helps you set up Telegram notifications for the trading bot.
It will guide you through getting your bot token and chat ID.
"""

import os
import requests
import json


def get_bot_token():
    """Get bot token from user with instructions."""
    print("🤖 STEP 1: Create a Telegram Bot")
    print("=" * 50)
    print("1. Open Telegram and search for @BotFather")
    print("2. Send /newbot to BotFather")
    print("3. Follow the instructions to create your bot")
    print("4. Copy the bot token (looks like: 123456789:ABCdefGHIjklMNOpqrsTUVwxyz)")
    print()
    
    while True:
        token = input("Enter your bot token: ").strip()
        if token and ':' in token:
            return token
        print("❌ Invalid token format. Please try again.")


def get_chat_id(bot_token):
    """Get chat ID by having user message the bot."""
    print("\n💬 STEP 2: Get Your Chat ID")
    print("=" * 50)
    print("1. Search for your bot in Telegram (use the username you created)")
    print("2. Send any message to your bot (like 'hello')")
    print("3. Press Enter here after sending the message")
    
    input("Press Enter after you've sent a message to your bot...")
    
    try:
        # Get updates from Telegram
        url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
        response = requests.get(url, timeout=10)
        data = response.json()
        
        if not data.get('ok'):
            print(f"❌ Error: {data.get('description', 'Unknown error')}")
            return None
        
        updates = data.get('result', [])
        if not updates:
            print("❌ No messages found. Make sure you sent a message to your bot.")
            return None
        
        # Get the most recent chat ID
        latest_update = updates[-1]
        chat_id = latest_update['message']['chat']['id']
        
        print(f"✅ Found chat ID: {chat_id}")
        return str(chat_id)
        
    except Exception as e:
        print(f"❌ Error getting chat ID: {e}")
        return None


def test_telegram(bot_token, chat_id):
    """Test sending a message to verify setup."""
    print("\n🧪 STEP 3: Testing Telegram Setup")
    print("=" * 50)
    
    try:
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        data = {
            'chat_id': chat_id,
            'text': '🚀 Trading Bot Setup Complete!\n\nYour Telegram notifications are now working correctly.'
        }
        
        response = requests.post(url, json=data, timeout=10)
        result = response.json()
        
        if result.get('ok'):
            print("✅ Test message sent successfully!")
            print("Check your Telegram to see the test message.")
            return True
        else:
            print(f"❌ Failed to send test message: {result.get('description')}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Telegram: {e}")
        return False


def save_config(bot_token, chat_id):
    """Save configuration to environment variables."""
    print("\n💾 STEP 4: Saving Configuration")
    print("=" * 50)
    
    # For Windows
    if os.name == 'nt':
        try:
            os.system(f'setx TELEGRAM_BOT_TOKEN "{bot_token}"')
            os.system(f'setx TELEGRAM_CHAT_ID "{chat_id}"')
            os.system('setx TELEGRAM_ENABLED "true"')
            print("✅ Environment variables set for Windows")
            print("⚠️  Please restart your command prompt/IDE for changes to take effect")
        except Exception as e:
            print(f"❌ Error setting Windows environment variables: {e}")
    
    # Also create a .env file
    try:
        with open('.env', 'w') as f:
            f.write(f"TELEGRAM_BOT_TOKEN={bot_token}\n")
            f.write(f"TELEGRAM_CHAT_ID={chat_id}\n")
            f.write("TELEGRAM_ENABLED=true\n")
        print("✅ Created .env file with Telegram configuration")
    except Exception as e:
        print(f"❌ Error creating .env file: {e}")
    
    # Show manual setup instructions
    print("\n📋 Manual Setup (if automatic setup failed):")
    print("Add these to your environment variables:")
    print(f"TELEGRAM_BOT_TOKEN={bot_token}")
    print(f"TELEGRAM_CHAT_ID={chat_id}")
    print("TELEGRAM_ENABLED=true")


def main():
    """Main setup function."""
    print("🔧 Telegram Trading Bot Setup")
    print("=" * 50)
    print("This script will help you set up Telegram notifications for your trading bot.")
    print()
    
    # Get bot token
    bot_token = get_bot_token()
    
    # Get chat ID
    chat_id = get_chat_id(bot_token)
    if not chat_id:
        print("❌ Setup failed. Please try again.")
        return
    
    # Test the setup
    if not test_telegram(bot_token, chat_id):
        print("❌ Setup failed. Please check your token and chat ID.")
        return
    
    # Save configuration
    save_config(bot_token, chat_id)
    
    print("\n🎉 Setup Complete!")
    print("=" * 50)
    print("Your trading bot is now configured to send Telegram notifications.")
    print("You can now run your bot and it will send trade alerts to Telegram.")
    print()
    print("To disable notifications, set TELEGRAM_ENABLED=false")


if __name__ == "__main__":
    main()
