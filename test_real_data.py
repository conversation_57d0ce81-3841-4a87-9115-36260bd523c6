"""
Test script to demonstrate the real-time market data fix.

This script shows how the new system:
1. Fetches real-time data from exchanges
2. Validates signal prices against current market prices
3. Rejects signals with price mismatches > 5%
"""

import sys
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_price_validation():
    """Test price validation with real market data."""
    try:
        from real_market_data import RealTimeMarketDataProvider
        from config import get_config
        
        logger.info("🚀 Testing Real-Time Market Data Provider")
        logger.info("=" * 60)
        
        # Initialize provider
        config = get_config()
        provider = RealTimeMarketDataProvider(config)
        
        # Test cases with different price scenarios
        test_cases = [
            {
                'symbol': 'DOTUSDT',
                'signal_price': 7.32,  # Your example - should be rejected
                'description': 'Outdated DOTUSDT price (your example)'
            },
            {
                'symbol': 'BTCUSDT', 
                'signal_price': 95000.0,  # Current BTC price
                'description': 'Current BTCUSDT price (should be accepted)'
            },
            {
                'symbol': 'ETHUSDT',
                'signal_price': 5000.0,  # Slightly off ETH price
                'description': 'Slightly off ETHUSDT price'
            }
        ]
        
        logger.info("📊 Testing price validation for different scenarios:")
        logger.info("")
        
        for i, test_case in enumerate(test_cases, 1):
            logger.info(f"Test {i}: {test_case['description']}")
            logger.info(f"Symbol: {test_case['symbol']}")
            logger.info(f"Signal Price: ${test_case['signal_price']:.2f}")
            
            try:
                result = provider.validate_signal_price(
                    test_case['symbol'], 
                    test_case['signal_price'],
                    threshold=0.05  # 5% threshold
                )
                
                logger.info(f"Current Price: ${result['current_price']:.6f}")
                logger.info(f"Price Difference: {result['price_difference_percent']:.2f}%")
                logger.info(f"Validation Result: {result['recommendation']}")
                
                if result['recommendation'] == 'REJECT':
                    logger.warning(f"❌ SIGNAL REJECTED: {result.get('reason', 'Unknown reason')}")
                else:
                    logger.info(f"✅ SIGNAL ACCEPTED")
                    
            except Exception as e:
                logger.error(f"💥 Error testing {test_case['symbol']}: {e}")
            
            logger.info("-" * 40)
            
        logger.info("")
        logger.info("🎯 Summary:")
        logger.info("- Signals with price differences > 5% are automatically rejected")
        logger.info("- This prevents trading on outdated or incorrect prices")
        logger.info("- Real-time validation ensures signal accuracy")
        
    except ImportError as e:
        logger.error(f"💥 Import error: {e}")
        logger.error("Make sure CCXT is installed: pip install ccxt")
    except Exception as e:
        logger.error(f"💥 Test failed: {e}")
        import traceback
        traceback.print_exc()

def test_real_data_fetch():
    """Test fetching real OHLCV data."""
    try:
        from real_market_data import RealTimeMarketDataProvider
        from config import get_config
        
        logger.info("📈 Testing Real-Time Data Fetching")
        logger.info("=" * 60)
        
        config = get_config()
        provider = RealTimeMarketDataProvider(config)
        
        # Test fetching data for a few symbols
        symbols = ['BTCUSDT', 'ETHUSDT', 'DOTUSDT']
        timeframes = ['1h']
        
        logger.info(f"Fetching data for: {', '.join(symbols)}")
        logger.info(f"Timeframes: {', '.join(timeframes)}")
        logger.info("")
        
        market_data = provider.get_current_data(symbols, timeframes)
        
        for symbol in symbols:
            if symbol in market_data and '1h' in market_data[symbol]:
                df = market_data[symbol]['1h']
                if hasattr(df, 'attrs') and df.attrs:
                    attrs = df.attrs
                    logger.info(f"📊 {symbol}:")
                    logger.info(f"   Latest Close: ${attrs.get('latest_close', 0):.6f}")
                    logger.info(f"   Current Price: ${attrs.get('current_price', 0):.6f}")
                    logger.info(f"   Price Valid: {attrs.get('price_valid', False)}")
                    logger.info(f"   Candles: {len(df)}")
                else:
                    logger.warning(f"⚠️ No metadata for {symbol}")
            else:
                logger.error(f"💥 No data for {symbol}")
        
        logger.info("")
        logger.info("✅ Real-time data fetching test completed")
        
    except Exception as e:
        logger.error(f"💥 Data fetch test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    logger.info("🔧 TRADING BOT PRICE VALIDATION FIX TEST")
    logger.info("=" * 60)
    logger.info("")
    
    # Test price validation
    test_price_validation()
    
    logger.info("")
    logger.info("=" * 60)
    logger.info("")
    
    # Test data fetching
    test_real_data_fetch()
    
    logger.info("")
    logger.info("🎉 All tests completed!")
