"""
Test Telegram Configuration

This script tests if Telegram is properly configured and working.
"""

import os
from config import get_telegram_config
from notify import send_telegram

def test_environment_variables():
    """Test if environment variables are set."""
    print("🔍 Testing Environment Variables:")
    print("=" * 40)
    
    telegram_enabled = os.getenv('TELEGRAM_ENABLED')
    telegram_token = os.getenv('TELEGRAM_BOT_TOKEN')
    telegram_chat_id = os.getenv('TELEGRAM_CHAT_ID')
    
    print(f"TELEGRAM_ENABLED: {telegram_enabled}")
    print(f"TELEGRAM_BOT_TOKEN: {'Set' if telegram_token else 'Not set'}")
    print(f"TELEGRAM_CHAT_ID: {telegram_chat_id}")
    print()
    
    return telegram_enabled == 'true' and telegram_token and telegram_chat_id

def test_config_loading():
    """Test if config.py loads the values correctly."""
    print("🔧 Testing Config Loading:")
    print("=" * 40)
    
    try:
        config = get_telegram_config()
        print(f"telegram_enabled: {config.get('telegram_enabled')}")
        print(f"telegram_token: {'Set' if config.get('telegram_token') and config.get('telegram_token') != 'your_telegram_bot_token' else 'Not set'}")
        print(f"telegram_chat_id: {config.get('telegram_chat_id') if config.get('telegram_chat_id') != 'your_telegram_chat_id' else 'Not set'}")
        print(f"send_charts: {config.get('send_charts')}")
        print()
        
        return (config.get('telegram_enabled') and 
                config.get('telegram_token') and 
                config.get('telegram_token') != 'your_telegram_bot_token' and
                config.get('telegram_chat_id') and 
                config.get('telegram_chat_id') != 'your_telegram_chat_id')
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return False

def test_telegram_sending():
    """Test sending a Telegram message."""
    print("📱 Testing Telegram Message Sending:")
    print("=" * 40)
    
    try:
        result = send_telegram("🧪 Test message from trading bot configuration test!")
        if result:
            print("✅ Telegram message sent successfully!")
            print("Check your Telegram to see the test message.")
        else:
            print("❌ Failed to send Telegram message.")
        return result
    except Exception as e:
        print(f"❌ Error sending Telegram message: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 Telegram Configuration Test")
    print("=" * 50)
    print()
    
    # Test 1: Environment variables
    env_ok = test_environment_variables()
    
    # Test 2: Config loading
    config_ok = test_config_loading()
    
    # Test 3: Telegram sending (only if config is OK)
    telegram_ok = False
    if config_ok:
        telegram_ok = test_telegram_sending()
    else:
        print("📱 Skipping Telegram test (config not loaded)")
        print("=" * 40)
    
    # Summary
    print("📊 Test Summary:")
    print("=" * 40)
    print(f"Environment Variables: {'✅ PASS' if env_ok else '❌ FAIL'}")
    print(f"Config Loading: {'✅ PASS' if config_ok else '❌ FAIL'}")
    print(f"Telegram Sending: {'✅ PASS' if telegram_ok else '❌ FAIL' if config_ok else '⏭️ SKIPPED'}")
    print()
    
    if env_ok and config_ok and telegram_ok:
        print("🎉 All tests passed! Telegram is working correctly.")
    elif not env_ok:
        print("⚠️ Environment variables not loaded. Please restart your terminal.")
        print("Or run these commands in your current session:")
        print('$env:TELEGRAM_ENABLED="true"')
        print('$env:TELEGRAM_BOT_TOKEN="**********************************************"')
        print('$env:TELEGRAM_CHAT_ID="1315592475"')
    elif not config_ok:
        print("⚠️ Config loading failed. Check config.py")
    else:
        print("⚠️ Some tests failed. Check the output above.")

if __name__ == "__main__":
    main()
