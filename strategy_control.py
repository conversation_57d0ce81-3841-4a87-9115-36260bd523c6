"""
Strategy Control Module

This module controls which trading strategies are enabled or disabled.

Usage:
    1. Set strategy switches in STRATEGY_SWITCHES dictionary below
    2. Key = strategy filename (without .py extension)
    3. Value = True (enabled) or False (disabled)

    Example:
        "rsi_divergence": True    # strategies/rsi_divergence.py will be loaded
        "breakout_zone": False    # strategies/breakout_zone.py will be ignored

    The strategy_loader.py will automatically load only the strategies
    that are set to True in this dictionary.

    To add a new strategy:
    1. Create the strategy file in strategies/ folder
    2. Add an entry here with the filename (without .py)
    3. Set to True to enable, False to disable
"""

from typing import Dict, Any

# =============================================================================
# STRATEGY SWITCHES - EDIT THIS DICTIONARY TO ENABLE/DISABLE STRATEGIES
# =============================================================================

STRATEGY_SWITCHES = {
    # Technical Analysis Strategies
    "rsi_divergence": True,
    "bollinger_bands": True,
    "moving_average_crossover": False,
    "macd_crossover": False,
    "stochastic_rsi": False,
    "support_resistance": False,
    "fibonacci_retracement": False,
    "volume_profile": False,

    # Momentum & Trend Strategies
    "momentum_breakout": True,
    "trend_following": False,
    "mean_reversion": False,
    "breakout_zone": False,
    "ema_trend": True,

    # Pattern Recognition Strategies
    "head_shoulders": False,
    "double_top_bottom": False,
    "triangle_patterns": False,
    "flag_pennant": False,
    "wedge_patterns": False,

    # Advanced Strategies
    "grid_trading": False,
    "dca_strategy": False,
    "scalping_strategy": False,
    "swing_trading": False,

    # AI/ML Strategies
    "lstm_prediction": False,
    "sentiment_analysis": False,
    "news_impact": False,
    "ml_ensemble": False,

    # Arbitrage Strategies
    "cross_exchange_arbitrage": False,
    "triangular_arbitrage": False,
    "statistical_arbitrage": False,

    # Risk Management Strategies
    "portfolio_rebalancing": False,
    "volatility_adjustment": False,
    "dynamic_hedging": False,
}

# =============================================================================
# STRATEGY PARAMETERS - CONFIGURE STRATEGY-SPECIFIC SETTINGS
# =============================================================================

STRATEGY_PARAMS = {
    "rsi_divergence": {
        "rsi_period": 14,
        "divergence_lookback": 20,
        "min_divergence_strength": 0.7,
        "timeframes": ["1h", "4h"],
        "min_rsi_oversold": 30,
        "max_rsi_overbought": 70,
    },

    "bollinger_bands": {
        "period": 20,
        "std_dev": 2,
        "squeeze_threshold": 0.1,
        "timeframes": ["1h", "4h"],
        "mean_reversion_mode": True,
        "breakout_mode": False,
    },

    "moving_average_crossover": {
        "fast_ma": 50,
        "slow_ma": 200,
        "ma_type": "EMA",
        "timeframes": ["1h", "4h"],
        "volume_confirmation": True,
        "trend_filter": True,
    },

    "momentum_breakout": {
        "volume_threshold": 1.5,
        "price_change_threshold": 0.02,
        "confirmation_candles": 2,
        "timeframes": ["15m", "1h"],
        "atr_multiplier": 2.0,
    },

    "ema_trend": {
        "fast_ema": 12,
        "slow_ema": 26,
        "signal_ema": 9,
        "timeframes": ["1h", "4h"],
        "trend_strength_threshold": 0.5,
    },

    "macd_crossover": {
        "fast_period": 12,
        "slow_period": 26,
        "signal_period": 9,
        "timeframes": ["15m", "1h"],
        "histogram_threshold": 0.0,
    },

    "portfolio_rebalancing": {
        "rebalance_frequency": "24h",
        "max_position_size": 0.1,
        "correlation_threshold": 0.7,
        "target_allocation": {"BTC": 0.4, "ETH": 0.3, "ALT": 0.3},
    },

    "volatility_adjustment": {
        "atr_period": 14,
        "volatility_multiplier": 2.0,
        "max_risk_per_trade": 0.02,
        "volatility_lookback": 20,
    },
}


class StrategyControl:
    """
    Utility class for managing strategy switches and parameters.
    """

    @staticmethod
    def is_strategy_enabled(strategy_name: str) -> bool:
        """
        Check if a strategy is enabled.

        Args:
            strategy_name: Name of the strategy (filename without .py)

        Returns:
            bool: True if strategy is enabled, False otherwise
        """
        return STRATEGY_SWITCHES.get(strategy_name, False)

    @staticmethod
    def get_enabled_strategies() -> list:
        """
        Get list of all enabled strategies.

        Returns:
            list: List of enabled strategy names
        """
        return [name for name, enabled in STRATEGY_SWITCHES.items() if enabled]

    @staticmethod
    def get_strategy_params(strategy_name: str) -> Dict[str, Any]:
        """
        Get parameters for a specific strategy.

        Args:
            strategy_name: Name of the strategy

        Returns:
            dict: Strategy parameters or empty dict if not found
        """
        return STRATEGY_PARAMS.get(strategy_name, {})

    @staticmethod
    def get_all_strategies() -> Dict[str, bool]:
        """
        Get all strategies and their enabled status.

        Returns:
            dict: Dictionary of all strategies and their status
        """
        return STRATEGY_SWITCHES.copy()

    @staticmethod
    def count_enabled_strategies() -> int:
        """
        Count the number of enabled strategies.

        Returns:
            int: Number of enabled strategies
        """
        return sum(1 for enabled in STRATEGY_SWITCHES.values() if enabled)

    @staticmethod
    def enable_strategy(strategy_name: str) -> bool:
        """
        Enable a strategy (runtime modification).

        Args:
            strategy_name: Name of the strategy to enable

        Returns:
            bool: True if strategy exists and was enabled, False otherwise
        """
        if strategy_name in STRATEGY_SWITCHES:
            STRATEGY_SWITCHES[strategy_name] = True
            return True
        return False

    @staticmethod
    def disable_strategy(strategy_name: str) -> bool:
        """
        Disable a strategy (runtime modification).

        Args:
            strategy_name: Name of the strategy to disable

        Returns:
            bool: True if strategy exists and was disabled, False otherwise
        """
        if strategy_name in STRATEGY_SWITCHES:
            STRATEGY_SWITCHES[strategy_name] = False
            return True
        return False

    @staticmethod
    def toggle_strategy(strategy_name: str) -> bool:
        """
        Toggle a strategy's enabled state.

        Args:
            strategy_name: Name of the strategy to toggle

        Returns:
            bool: New state of the strategy (True if enabled, False if disabled)
        """
        if strategy_name in STRATEGY_SWITCHES:
            STRATEGY_SWITCHES[strategy_name] = not STRATEGY_SWITCHES[strategy_name]
            return STRATEGY_SWITCHES[strategy_name]
        return False
