"""
Strategy control module for enabling/disabling trading strategies.
Contains boolean flags to turn strategies ON/OFF.
"""

from typing import Dict, Any


class StrategyControl:
    """
    Centralized control for enabling/disabling trading strategies.
    """
    
    # Strategy enable/disable flags
    STRATEGY_FLAGS = {
        # Technical Analysis Strategies
        'rsi_divergence': True,
        'macd_crossover': False,
        'bollinger_bands': True,
        'support_resistance': False,
        'moving_average_crossover': True,
        'stochastic_rsi': False,
        'fibonacci_retracement': False,
        'volume_profile': False,
        
        # Pattern Recognition Strategies
        'head_shoulders': False,
        'double_top_bottom': False,
        'triangle_patterns': False,
        'flag_pennant': False,
        
        # Momentum Strategies
        'momentum_breakout': True,
        'trend_following': False,
        'mean_reversion': False,
        
        # AI/ML Strategies
        'lstm_prediction': False,
        'sentiment_analysis': False,
        'news_impact': False,
        
        # Arbitrage Strategies
        'cross_exchange_arbitrage': False,
        'triangular_arbitrage': False,
        
        # Risk Management Strategies
        'portfolio_rebalancing': True,
        'volatility_adjustment': True,
    }
    
    # Strategy-specific parameters
    STRATEGY_PARAMS = {
        'rsi_divergence': {
            'rsi_period': 14,
            'divergence_lookback': 20,
            'min_divergence_strength': 0.7,
            'timeframes': ['1h', '4h'],
        },
        'macd_crossover': {
            'fast_period': 12,
            'slow_period': 26,
            'signal_period': 9,
            'timeframes': ['15m', '1h'],
        },
        'bollinger_bands': {
            'period': 20,
            'std_dev': 2,
            'squeeze_threshold': 0.1,
            'timeframes': ['1h', '4h'],
        },
        'moving_average_crossover': {
            'fast_ma': 50,
            'slow_ma': 200,
            'ma_type': 'EMA',
            'timeframes': ['1h', '4h'],
        },
        'momentum_breakout': {
            'volume_threshold': 1.5,
            'price_change_threshold': 0.02,
            'confirmation_candles': 2,
            'timeframes': ['15m', '1h'],
        },
        'portfolio_rebalancing': {
            'rebalance_frequency': '24h',
            'max_position_size': 0.1,
            'correlation_threshold': 0.7,
        },
        'volatility_adjustment': {
            'atr_period': 14,
            'volatility_multiplier': 2.0,
            'max_risk_per_trade': 0.02,
        }
    }
    
    @classmethod
    def is_strategy_enabled(cls, strategy_name: str) -> bool:
        """
        Check if a strategy is enabled.
        
        Args:
            strategy_name: Name of the strategy
            
        Returns:
            bool: True if strategy is enabled, False otherwise
        """
        return cls.STRATEGY_FLAGS.get(strategy_name, False)
    
    @classmethod
    def get_enabled_strategies(cls) -> list:
        """
        Get list of all enabled strategies.
        
        Returns:
            list: List of enabled strategy names
        """
        return [name for name, enabled in cls.STRATEGY_FLAGS.items() if enabled]
    
    @classmethod
    def get_strategy_params(cls, strategy_name: str) -> Dict[str, Any]:
        """
        Get parameters for a specific strategy.
        
        Args:
            strategy_name: Name of the strategy
            
        Returns:
            dict: Strategy parameters
        """
        return cls.STRATEGY_PARAMS.get(strategy_name, {})
    
    @classmethod
    def enable_strategy(cls, strategy_name: str):
        """
        Enable a strategy.
        
        Args:
            strategy_name: Name of the strategy to enable
        """
        if strategy_name in cls.STRATEGY_FLAGS:
            cls.STRATEGY_FLAGS[strategy_name] = True
    
    @classmethod
    def disable_strategy(cls, strategy_name: str):
        """
        Disable a strategy.
        
        Args:
            strategy_name: Name of the strategy to disable
        """
        if strategy_name in cls.STRATEGY_FLAGS:
            cls.STRATEGY_FLAGS[strategy_name] = False
    
    @classmethod
    def toggle_strategy(cls, strategy_name: str):
        """
        Toggle a strategy's enabled state.
        
        Args:
            strategy_name: Name of the strategy to toggle
        """
        if strategy_name in cls.STRATEGY_FLAGS:
            cls.STRATEGY_FLAGS[strategy_name] = not cls.STRATEGY_FLAGS[strategy_name]
    
    @classmethod
    def get_strategy_status(cls) -> Dict[str, bool]:
        """
        Get the status of all strategies.
        
        Returns:
            dict: Dictionary of strategy names and their enabled status
        """
        return cls.STRATEGY_FLAGS.copy()
